{"workbench.colorCustomizations": {"editor.foldBackground": "#1e1e2e11"}, "peacock.color": "#fb0503", "svg.preview.background": "dark-transparent", "mdx-preview.preview.useVscodeMarkdownStyles": false, "editor.maxTokenizationLineLength": 50000, "editor.fontFamily": "'Operator Mono Lig', 'PragmataPro Mono', 'Operator Mono Lig Light', 'Operator Mono Ssm Light', 'Operator Mono Lig', 'Operator Mono Ssm Lig', 'Operator Mono', 'M PLUS 1 Code', 'SF Mono', 'Inconsolata XL', 'Input Mono Condensed', Menlo, Monaco, 'Courier New', monospace", "editor.lineHeight": 23, "editor.fontSize": 17, "editor.fontWeight": 400, "editor.fontLigatures": "'calt', 'liga', 'dlig', 'ss01', 'ss02', 'ss03', 'ss04', 'ss05', 'ss06', 'ss07', 'ss08'", "terminal.integrated.drawBoldTextInBrightColors": true, "terminal.integrated.tabs.location": "right", "python.experiments.optOutFrom": ["pythonTerminalEnvVarActivation"], "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "breadcrumbs.icons": false, "breadcrumbs.filePath": "last", "markdown.validate.ignoredLinks": ["/Users/<USER>/_current/media-archive-rr/.local/._notes/_notes.md"]}