import type { ReactNode } from 'react'

interface LoadingSpinnerProps {
	size?: 'sm' | 'md' | 'lg' | 'xl'
	className?: string
	children?: ReactNode
}

const sizeClasses = {
	sm: 'h-4 w-4',
	md: 'h-8 w-8',
	lg: 'h-12 w-12',
	xl: 'h-16 w-16',
}

export default function LoadingSpinner({ 
	size = 'md', 
	className = '',
	children 
}: LoadingSpinnerProps) {
	return (
		<div className={`flex flex-col items-center justify-center gap-4 ${className}`}>
			<div 
				className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-500`}
				role="status"
				aria-label="Loading"
			/>
			{children && (
				<div className="text-gray-400 text-sm font-medium">
					{children}
				</div>
			)}
		</div>
	)
}
