altgraph @ file:///AppleInternal/Library/BuildRoots/39d9dc1a-2111-11f0-be06-226177e5bb69/Library/Caches/com.apple.xbs/Sources/python3/altgraph-0.17.2-py2.py3-none-any.whl
appdirs==1.4.4
Arpeggio==2.0.2
bitarray==3.4.2
bitmath==1.3.3.1
bitstring==4.3.1
black==23.3.0
blessed==1.17.12
bpylist2==4.1.1
certifi==2020.12.5
cffi==1.17.1
cgmetadata==0.2.0
chardet==4.0.0
charset-normalizer==3.0.1
click==8.1.3
cloup==2.1.2
commonmark==0.9.1
cryptography==39.0.0
datetime_tzutils==1.0.1
distlib==0.3.4
filelock==3.6.0
future @ file:///AppleInternal/Library/BuildRoots/39d9dc1a-2111-11f0-be06-226177e5bb69/Library/Caches/com.apple.xbs/Sources/python3/future-0.18.2-py3-none-any.whl
idna==2.10
importlib_metadata==8.7.0
jedi==0.19.2
lxml==5.4.0
mac-alias==2.2.2
macholib @ file:///AppleInternal/Library/BuildRoots/39d9dc1a-2111-11f0-be06-226177e5bb69/Library/Caches/com.apple.xbs/Sources/python3/macholib-1.15.2-py2.py3-none-any.whl
makelive==0.6.2
Mako==1.2.4
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdinfo @ file:///usr/local/mdinfo-0.2.0
mdurl==0.1.2
more-itertools==8.14.0
mypy-extensions==1.0.0
objexplore==1.6.3
osxmetadata==1.3.5
osxphotos==0.68.6
packaging==23.0
parso==0.8.4
pathspec==0.11.1
pathvalidate==2.5.2
pdfminer==20191125
pdfminer.six==20221105
photokit==0.2.1
photoscript==0.3.1
platformdirs==2.5.2
pluggy==1.5.0
prompt_toolkit==3.0.51
ptpython==3.0.30
py-applescript==1.0.3
pycparser==2.21
pycryptodome==3.16.0
Pygments==2.19.1
pyobjc-core==10.3.2
pyobjc-framework-AppleScriptKit==9.2
pyobjc-framework-AppleScriptObjC==9.2
pyobjc-framework-AVFoundation==9.2
pyobjc-framework-Cocoa==10.3.2
pyobjc-framework-Contacts==10.3.2
pyobjc-framework-CoreAudio==9.2
pyobjc-framework-CoreLocation==10.3.2
pyobjc-framework-CoreMedia==9.2
pyobjc-framework-CoreML==9.2
pyobjc-framework-CoreServices==9.2
pyobjc-framework-FSEvents==9.2
pyobjc-framework-Metal==9.2
pyobjc-framework-Photos==9.2
pyobjc-framework-Quartz==9.2
pyobjc-framework-ScriptingBridge==9.2
pyobjc-framework-UniformTypeIdentifiers==9.2
pyobjc-framework-Vision==9.2
python-docx==0.8.11
pytimeparse2==1.4.0
PyYAML==6.0.2
requests==2.32.3
rich==13.9.4
rich-theme-manager==0.11.0
shortuuid==1.0.9
six @ file:///AppleInternal/Library/BuildRoots/39d9dc1a-2111-11f0-be06-226177e5bb69/Library/Caches/com.apple.xbs/Sources/python3/six-1.15.0-py2.py3-none-any.whl
strpdatetime==0.4.0
tenacity==8.5.0
textX==4.2.2
tinytag==1.10.1
toml==0.10.2
tomli==2.0.1
typing_extensions==4.5.0
urllib3==1.26.5
utitools==0.3.0
virtualenv==20.14.1
wcwidth==0.2.13
wrapt==1.17.2
wurlitzer==3.1.1
xattr==1.1.4
xdg==5.1.1
zipp==3.21.0
