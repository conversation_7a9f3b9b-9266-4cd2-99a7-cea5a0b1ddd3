{"name": "media", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "eslintIgnore": ["/node_modules", "/build", "/public/build", "/playwright-report", "/server-build"], "dependencies": {"@epic-web/invariant": "^1.0.0", "@prisma/client": "^6.8.2", "@react-router/node": "^7.6.2", "@react-router/serve": "^7.6.2", "better-sqlite3": "^11.9.0", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.6.2", "sqlite3": "^5.1.7", "vite-plugin-devtools-json": "^0.1.0"}, "devDependencies": {"@react-router/dev": "^7.6.2", "@tailwindcss/vite": "^4.1.4", "@types/better-sqlite3": "^7.6.13", "@types/eslint": "^8.56.12", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "prettier": "^3.5.3", "prettier-eslint": "^16.4.2", "prettier-plugin-sql": "^0.19.1", "prisma": "^6.8.2", "react-router-devtools": "^5.0.6", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-env-only": "^3.0.3", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=20.0.0"}}