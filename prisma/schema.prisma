generator client {
  provider      = "prisma-client"
  output        = "./generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model albums {
  uuid         String         @id
  title        String?
  photo_albums photo_albums[]
}

model face_age_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_ethnicity_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_expression_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_eye_makeup_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_eyes_closed_types {
  left_eye_closed  Int     @default(0)
  right_eye_closed Int     @default(0)
  description      String?

  @@id([left_eye_closed, right_eye_closed])
}

model face_eyes_state_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_facial_hair_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_gender_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_glasses_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_hair_color_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_lip_makeup_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_mask_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_pose_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_skin_tone_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model face_smile_combined_types {
  has_smile   Int     @default(0)
  smile_type  Int     @default(0)
  description String?

  @@id([has_smile, smile_type])
}

model face_smile_types {
  id          Int     @id @default(autoincrement())
  description String?
}

model faces {
  uuid             String   @id
  photo_uuid       String
  person_uuid      String?
  center_x         Float?
  center_y         Float?
  size             Float?
  quality          Float?
  source_width     Int?
  source_height    Int?
  has_smile        Int?
  manual           Int?
  face_type        String?
  age_type         Int?
  eye_makeup_type  Int?
  eye_state        Int?
  facial_hair_type Int?
  gender_type      Int?
  glasses_type     Int?
  hair_color_type  Int?
  intrash          Int?
  lip_makeup_type  Int?
  smile_type       Int?
  persons          persons? @relation(fields: [person_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)
  photos           photos   @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)
}

model keywords {
  photo_uuid String
  keyword    String
  photos     photos @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([photo_uuid, keyword])
}

model labels {
  photo_uuid String
  label      String
  photos     photos @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([photo_uuid, label])
}

model persons {
  uuid          String          @id
  name          String?
  display_name  String?
  faces         faces[]
  photo_persons photo_persons[]
}

model photo_albums {
  photo_uuid String
  album_uuid String
  albums     albums @relation(fields: [album_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)
  photos     photos @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([photo_uuid, album_uuid])
}

model photo_persons {
  photo_uuid  String
  person_uuid String
  persons     persons @relation(fields: [person_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)
  photos      photos  @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([photo_uuid, person_uuid])
}

model photos {
  uuid              String           @id
  original_filename String?
  date              String?
  title             String?
  path              String?
  path_edited       String?
  has_raw           Int?
  height            Int?
  width             Int?
  favorite          Int?
  hidden            Int?
  latitude          Float?
  longitude         Float?
  portrait          Int?
  hdr               Int?
  panorama          Int?
  description       String?
  faces             faces[]
  keywords          keywords[]
  labels            labels[]
  photo_albums      photo_albums[]
  photo_persons     photo_persons[]
  place_addresses   place_addresses?
  place_names       place_names[]
  places            places?
}

model place_addresses {
  photo_uuid       String  @id
  street           String?
  city             String?
  state_province   String?
  postal_code      String?
  country          String?
  iso_country_code String?
  photos           photos  @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)
}

model place_names {
  photo_uuid String
  name_type  String
  name       String
  photos     photos @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([photo_uuid, name_type, name])
}

model places {
  photo_uuid   String  @id
  name         String?
  country_code String?
  address_str  String?
  photos       photos  @relation(fields: [photo_uuid], references: [uuid], onDelete: NoAction, onUpdate: NoAction)
}
