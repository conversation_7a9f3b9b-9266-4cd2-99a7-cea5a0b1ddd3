version = 1
revision = 2
requires-python = ">=3.13"
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
    "platform_release < '22.0' and sys_platform == 'darwin'",
    "sys_platform != 'darwin'",
]

[[package]]
name = "ansicon"
version = "1.89.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/b6/e2/1c866404ddbd280efedff4a9f15abfe943cb83cde6e895022370f3a61f85/ansicon-1.89.0.tar.gz", hash = "sha256:e4d039def5768a47e4afec8e89e83ec3ae5a26bf00ad851f914d1240b444d2b1", size = 67312, upload-time = "2019-04-29T20:23:57.314Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/75/f9/f1c10e223c7b56a38109a3f2eb4e7fe9a757ea3ed3a166754fb30f65e466/ansicon-1.89.0-py2.py3-none-any.whl", hash = "sha256:f1def52d17f65c2c9682cf8370c03f541f410c1752d6a14029f97318e4b9dfec", size = 63675, upload-time = "2019-04-29T20:23:53.83Z" },
]

[[package]]
name = "appdirs"
version = "1.4.4"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d7/d8/05696357e0311f5b5c316d7b95f46c669dd9c15aaeecbb48c7d0aeb88c40/appdirs-1.4.4.tar.gz", hash = "sha256:7d5d0167b2b1ba821647616af46a749d1c653740dd0d2415100fe26e27afdf41", size = 13470, upload-time = "2020-05-11T07:59:51.037Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/3b/00/2344469e2084fb287c2e0b57b72910309874c3245463acd6cf5e3db69324/appdirs-1.4.4-py2.py3-none-any.whl", hash = "sha256:a841dacd6b99318a741b166adb07e19ee71a274450e68237b4650ca1055ab128", size = 9566, upload-time = "2020-05-11T07:59:49.499Z" },
]

[[package]]
name = "arpeggio"
version = "2.0.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/12/c4/516bb54456f85ad1947702ea4cef543a59de66d31a9887dbc3d9df36e3e1/Arpeggio-2.0.2.tar.gz", hash = "sha256:c790b2b06e226d2dd468e4fbfb5b7f506cec66416031fde1441cf1de2a0ba700", size = 766643, upload-time = "2023-07-09T12:30:04.737Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f7/4f/d28bf30a19d4649b40b501d531b44e73afada99044df100380fd9567e92f/Arpeggio-2.0.2-py2.py3-none-any.whl", hash = "sha256:f7c8ae4f4056a89e020c24c7202ac8df3e2bc84e416746f20b0da35bb1de0250", size = 55287, upload-time = "2023-07-09T12:30:01.87Z" },
]

[[package]]
name = "bitarray"
version = "3.4.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/b8/0d/15826c7c2d49a4518a1b24b0d432f1ecad2e0b68168f942058b5de498498/bitarray-3.4.2.tar.gz", hash = "sha256:78ed2b911aabede3a31e3329b1de8abdc8104bd5e0545184ddbd9c7f668f4059", size = 143756, upload-time = "2025-05-21T16:21:44.056Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f2/22/973d377477e1f27cf64f9e3292343219577136e32665a52667589380100d/bitarray-3.4.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:16263bdbb05ce379e7b8e9a9f3e0a61a9204a06a037bbc91322d2939b3079fd5", size = 141162, upload-time = "2025-05-21T16:19:06.488Z" },
    { url = "https://files.pythonhosted.org/packages/eb/53/65541b94fb6df1e8aa9a7359ac68f469c3243d8bc7302c5fb8ff8936dab2/bitarray-3.4.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:41fdc6fb8c3aabfcfe0073302c69fef0c74d6499491f133ba58755c3f2afb3d0", size = 138162, upload-time = "2025-05-21T16:19:07.688Z" },
]

[[package]]
name = "bitmath"
version = "*******"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/ec/ec/808245570e00df2e1fe8252903da309f18eb58768f44fecc0215dafbc386/bitmath-*******.tar.gz", hash = "sha256:293325f01e65defe966853111df11d39215eb705a967cb115851da8c4cfa3eb8", size = 88519, upload-time = "2018-08-23T16:23:53.031Z" }

[[package]]
name = "bitstring"
version = "4.3.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "bitarray", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/15/a8/a80c890db75d5bdd5314b5de02c4144c7de94fd0cefcae51acaeb14c6a3f/bitstring-4.3.1.tar.gz", hash = "sha256:a08bc09d3857216d4c0f412a1611056f1cc2b64fd254fb1e8a0afba7cfa1a95a", size = 251426, upload-time = "2025-03-22T09:39:06.978Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/75/2d/174566b533755ddf8efb32a5503af61c756a983de379f8ad3aed6a982d38/bitstring-4.3.1-py3-none-any.whl", hash = "sha256:69d1587f0ac18dc7d93fc7e80d5f447161a33e57027e726dc18a0a8bacf1711a", size = 71930, upload-time = "2025-03-22T09:39:05.163Z" },
]

[[package]]
name = "black"
version = "25.1.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "click" },
    { name = "mypy-extensions" },
    { name = "packaging" },
    { name = "pathspec" },
    { name = "platformdirs" },
]
sdist = { url = "https://files.pythonhosted.org/packages/94/49/26a7b0f3f35da4b5a65f081943b7bcd22d7002f5f0fb8098ec1ff21cb6ef/black-25.1.0.tar.gz", hash = "sha256:33496d5cd1222ad73391352b4ae8da15253c5de89b93a80b3e2c8d9a19ec2666", size = 649449, upload-time = "2025-01-29T04:15:40.373Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/98/87/0edf98916640efa5d0696e1abb0a8357b52e69e82322628f25bf14d263d1/black-25.1.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:8f0b18a02996a836cc9c9c78e5babec10930862827b1b724ddfe98ccf2f2fe4f", size = 1650673, upload-time = "2025-01-29T05:37:20.574Z" },
    { url = "https://files.pythonhosted.org/packages/52/e5/f7bf17207cf87fa6e9b676576749c6b6ed0d70f179a3d812c997870291c3/black-25.1.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:afebb7098bfbc70037a053b91ae8437c3857482d3a690fefc03e9ff7aa9a5fd3", size = 1453190, upload-time = "2025-01-29T05:37:22.106Z" },
    { url = "https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:030b9759066a4ee5e5aca28c3c77f9c64789cdd4de8ac1df642c40b708be6171", size = 1782926, upload-time = "2025-01-29T04:18:58.564Z" },
    { url = "https://files.pythonhosted.org/packages/cc/64/94eb5f45dcb997d2082f097a3944cfc7fe87e071907f677e80788a2d7b7a/black-25.1.0-cp313-cp313-win_amd64.whl", hash = "sha256:a22f402b410566e2d1c950708c77ebf5ebd5d0d88a6a2e87c86d9fb48afa0d18", size = 1442613, upload-time = "2025-01-29T04:19:27.63Z" },
    { url = "https://files.pythonhosted.org/packages/09/71/54e999902aed72baf26bca0d50781b01838251a462612966e9fc4891eadd/black-25.1.0-py3-none-any.whl", hash = "sha256:95e8176dae143ba9097f351d174fdaf0ccd29efb414b362ae3fd72bf0f710717", size = 207646, upload-time = "2025-01-29T04:15:38.082Z" },
]

[[package]]
name = "blessed"
version = "1.17.12"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "jinxed", marker = "sys_platform == 'win32'" },
    { name = "six" },
    { name = "wcwidth" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0e/e6/f02d17a5ac70ca2d5794b105b8d8e9b5513e8b15ca6955440c0dbc90f363/blessed-1.17.12.tar.gz", hash = "sha256:580429e7e0c6f6a42ea81b0ae5a4993b6205c6ccbb635d034b4277af8175753e", size = 6697754, upload-time = "2020-11-28T03:26:36.511Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/88/34/61e670039aefca011b5e6fb1a73de18165ef6d016ac16df423b20d719e64/blessed-1.17.12-py2.py3-none-any.whl", hash = "sha256:0a74a8d3f0366db600d061273df77d44f0db07daade7bb7a4d49c8bc22ed9f74", size = 76769, upload-time = "2020-11-28T03:26:04.52Z" },
]

[[package]]
name = "bpylist2"
version = "4.1.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/ca/34/eb90ff6be953f6e4df08d4e8c0b761bea144242b6d711e922113411cc631/bpylist2-4.1.1.tar.gz", hash = "sha256:0cc63284aee42f5c7e0ec87f8f59cdd35aaed05ad12d866b1868ea0c0caaafe1", size = 18000, upload-time = "2023-09-11T16:58:00.758Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8b/e5/b552c94fc965153c9dcad4b64202e5170d4918716bc082d4fa6c6230579c/bpylist2-4.1.1-py3-none-any.whl", hash = "sha256:4862eab78d9d908d532393208b6771cebc8debef99ab851b54a0a0e28e2bec6b", size = 16596, upload-time = "2023-09-11T16:57:59.526Z" },
]

[[package]]
name = "certifi"
version = "2025.4.26"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/e8/9e/c05b3920a3b7d20d3d3310465f50348e5b3694f4f88c6daf736eef3024c4/certifi-2025.4.26.tar.gz", hash = "sha256:0a816057ea3cdefcef70270d2c515e4506bbc954f417fa5ade2021213bb8f0c6", size = 160705, upload-time = "2025-04-26T02:12:29.51Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4a/7e/3db2bd1b1f9e95f7cddca6d6e75e2f2bd9f51b1246e546d88addca0106bd/certifi-2025.4.26-py3-none-any.whl", hash = "sha256:30350364dfe371162649852c63336a15c70c6510c2ad5015b21c2345311805f3", size = 159618, upload-time = "2025-04-26T02:12:27.662Z" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pycparser", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fc/97/c783634659c2920c3fc70419e3af40972dbaf758daa229a7d6ea6135c90d/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824", size = 516621, upload-time = "2024-09-04T20:45:21.852Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8d/f8/dd6c246b148639254dad4d6803eb6a54e8c85c6e11ec9df2cffa87571dbe/cffi-1.17.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f3a2b4222ce6b60e2e8b337bb9596923045681d71e5a082783484d845390938e", size = 182989, upload-time = "2024-09-04T20:44:28.956Z" },
    { url = "https://files.pythonhosted.org/packages/8b/f1/672d303ddf17c24fc83afd712316fda78dc6fce1cd53011b839483e1ecc8/cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:0984a4925a435b1da406122d4d7968dd861c1385afe3b45ba82b750f229811e2", size = 178802, upload-time = "2024-09-04T20:44:30.289Z" },
]

[[package]]
name = "cgmetadata"
version = "0.2.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-corelocation", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-corelocation", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "rich", marker = "sys_platform == 'darwin'" },
    { name = "utitools", marker = "sys_platform == 'darwin'" },
    { name = "wheel", marker = "sys_platform == 'darwin'" },
    { name = "wurlitzer", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/27/30/c0d164d67d8611eafb93329921884f46763a725533f05fa47e682c5c5aa5/cgmetadata-0.2.0.tar.gz", hash = "sha256:f6805dcb107bed1c736b3f0869c043637ffd8f8956b9bc7b4de6aa6e2876f5cf", size = 20583, upload-time = "2024-10-01T12:34:38.626Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e1/05/c735618c13d67d31a50d896d1f607d6c03fd70278c28c8eaa4c81fa6fdee/cgmetadata-0.2.0-py3-none-any.whl", hash = "sha256:531ee6c4d120c134c57589181bc5fb053bcdf9d7b949f33d3e5ab5db5d7d7f07", size = 19935, upload-time = "2024-10-01T12:34:37.384Z" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/e4/33/89c2ced2b67d1c2a61c19c6751aa8902d46ce3dacb23600a283619f5a12d/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63", size = 126367, upload-time = "2025-05-02T08:34:42.01Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ea/12/a93df3366ed32db1d907d7593a94f1fe6293903e3e92967bebd6950ed12c/charset_normalizer-3.4.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:926ca93accd5d36ccdabd803392ddc3e03e6d4cd1cf17deff3b989ab8e9dbcf0", size = 199622, upload-time = "2025-05-02T08:32:56.363Z" },
    { url = "https://files.pythonhosted.org/packages/04/93/bf204e6f344c39d9937d3c13c8cd5bbfc266472e51fc8c07cb7f64fcd2de/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:eba9904b0f38a143592d9fc0e19e2df0fa2e41c3c3745554761c5f6447eedabf", size = 143435, upload-time = "2025-05-02T08:32:58.551Z" },
    { url = "https://files.pythonhosted.org/packages/22/2a/ea8a2095b0bafa6c5b5a55ffdc2f924455233ee7b91c69b7edfcc9e02284/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:3fddb7e2c84ac87ac3a947cb4e66d143ca5863ef48e4a5ecb83bd48619e4634e", size = 153653, upload-time = "2025-05-02T08:33:00.342Z" },
    { url = "https://files.pythonhosted.org/packages/b6/57/1b090ff183d13cef485dfbe272e2fe57622a76694061353c59da52c9a659/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:98f862da73774290f251b9df8d11161b6cf25b599a66baf087c1ffe340e9bfd1", size = 146231, upload-time = "2025-05-02T08:33:02.081Z" },
    { url = "https://files.pythonhosted.org/packages/e2/28/ffc026b26f441fc67bd21ab7f03b313ab3fe46714a14b516f931abe1a2d8/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6c9379d65defcab82d07b2a9dfbfc2e95bc8fe0ebb1b176a3190230a3ef0e07c", size = 148243, upload-time = "2025-05-02T08:33:04.063Z" },
    { url = "https://files.pythonhosted.org/packages/c0/0f/9abe9bd191629c33e69e47c6ef45ef99773320e9ad8e9cb08b8ab4a8d4cb/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e635b87f01ebc977342e2697d05b56632f5f879a4f15955dfe8cef2448b51691", size = 150442, upload-time = "2025-05-02T08:33:06.418Z" },
    { url = "https://files.pythonhosted.org/packages/67/7c/a123bbcedca91d5916c056407f89a7f5e8fdfce12ba825d7d6b9954a1a3c/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:1c95a1e2902a8b722868587c0e1184ad5c55631de5afc0eb96bc4b0d738092c0", size = 145147, upload-time = "2025-05-02T08:33:08.183Z" },
    { url = "https://files.pythonhosted.org/packages/ec/fe/1ac556fa4899d967b83e9893788e86b6af4d83e4726511eaaad035e36595/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:ef8de666d6179b009dce7bcb2ad4c4a779f113f12caf8dc77f0162c29d20490b", size = 153057, upload-time = "2025-05-02T08:33:09.986Z" },
    { url = "https://files.pythonhosted.org/packages/2b/ff/acfc0b0a70b19e3e54febdd5301a98b72fa07635e56f24f60502e954c461/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:32fc0341d72e0f73f80acb0a2c94216bd704f4f0bce10aedea38f30502b271ff", size = 156454, upload-time = "2025-05-02T08:33:11.814Z" },
    { url = "https://files.pythonhosted.org/packages/92/08/95b458ce9c740d0645feb0e96cea1f5ec946ea9c580a94adfe0b617f3573/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:289200a18fa698949d2b39c671c2cc7a24d44096784e76614899a7ccf2574b7b", size = 154174, upload-time = "2025-05-02T08:33:13.707Z" },
    { url = "https://files.pythonhosted.org/packages/78/be/8392efc43487ac051eee6c36d5fbd63032d78f7728cb37aebcc98191f1ff/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:4a476b06fbcf359ad25d34a057b7219281286ae2477cc5ff5e3f70a246971148", size = 149166, upload-time = "2025-05-02T08:33:15.458Z" },
    { url = "https://files.pythonhosted.org/packages/44/96/392abd49b094d30b91d9fbda6a69519e95802250b777841cf3bda8fe136c/charset_normalizer-3.4.2-cp313-cp313-win32.whl", hash = "sha256:aaeeb6a479c7667fbe1099af9617c83aaca22182d6cf8c53966491a0f1b7ffb7", size = 98064, upload-time = "2025-05-02T08:33:17.06Z" },
    { url = "https://files.pythonhosted.org/packages/e9/b0/0200da600134e001d91851ddc797809e2fe0ea72de90e09bec5a2fbdaccb/charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl", hash = "sha256:aa6af9e7d59f9c12b33ae4e9450619cf2488e2bbe9b44030905877f0b2324980", size = 105641, upload-time = "2025-05-02T08:33:18.753Z" },
    { url = "https://files.pythonhosted.org/packages/20/94/c5790835a017658cbfabd07f3bfb549140c3ac458cfc196323996b10095a/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0", size = 52626, upload-time = "2025-05-02T08:34:40.053Z" },
]

[[package]]
name = "click"
version = "8.2.1"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/60/6c/8ca2efa64cf75a977a0d7fac081354553ebe483345c734fb6b6515d96bbc/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202", size = 286342, upload-time = "2025-05-20T23:19:49.832Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b", size = 102215, upload-time = "2025-05-20T23:19:47.796Z" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697, upload-time = "2022-10-25T02:36:22.414Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335, upload-time = "2022-10-25T02:36:20.889Z" },
]

[[package]]
name = "flake8"
version = "7.2.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "mccabe" },
    { name = "pycodestyle" },
    { name = "pyflakes" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e7/c4/5842fc9fc94584c455543540af62fd9900faade32511fab650e9891ec225/flake8-7.2.0.tar.gz", hash = "sha256:fa558ae3f6f7dbf2b4f22663e5343b6b6023620461f8d4ff2019ef4b5ee70426", size = 48177, upload-time = "2025-03-29T20:08:39.329Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/83/5c/0627be4c9976d56b1217cb5187b7504e7fd7d3503f8bfd312a04077bd4f7/flake8-7.2.0-py2.py3-none-any.whl", hash = "sha256:93b92ba5bdb60754a6da14fa3b93a9361fd00a59632ada61fd7b130436c40343", size = 57786, upload-time = "2025-03-29T20:08:37.902Z" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", size = 190490, upload-time = "2024-09-15T18:07:39.745Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", size = 70442, upload-time = "2024-09-15T18:07:37.964Z" },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f2/97/ebf4da567aa6827c909642694d71c9fcf53e5b504f2d96afea02718862f3/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7", size = 4793, upload-time = "2025-03-19T20:09:59.721Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760", size = 6050, upload-time = "2025-03-19T20:10:01.071Z" },
]

[[package]]
name = "jedi"
version = "0.19.2"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "parso" },
]
sdist = { url = "https://files.pythonhosted.org/packages/72/3a/79a912fbd4d8dd6fbb02bf69afd3bb72cf0c729bb3063c6f4498603db17a/jedi-0.19.2.tar.gz", hash = "sha256:4770dc3de41bde3966b02eb84fbcf557fb33cce26ad23da12c742fb50ecb11f0", size = 1231287, upload-time = "2024-11-11T01:41:42.873Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c0/5a/9cac0c82afec3d09ccd97c8b6502d48f165f9124db81b4bcb90b4af974ee/jedi-0.19.2-py2.py3-none-any.whl", hash = "sha256:a8ef22bde8490f57fe5c7681a3c83cb58874daf72b4784de3cce5b6ef6edb5b9", size = 1572278, upload-time = "2024-11-11T01:41:40.175Z" },
]

[[package]]
name = "jinxed"
version = "1.3.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "ansicon", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/20/d0/59b2b80e7a52d255f9e0ad040d2e826342d05580c4b1d7d7747cfb8db731/jinxed-1.3.0.tar.gz", hash = "sha256:1593124b18a41b7a3da3b078471442e51dbad3d77b4d4f2b0c26ab6f7d660dbf", size = 80981, upload-time = "2024-07-31T22:39:18.854Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/27/e3/0e0014d6ab159d48189e92044ace13b1e1fe9aa3024ba9f4e8cf172aa7c2/jinxed-1.3.0-py2.py3-none-any.whl", hash = "sha256:b993189f39dc2d7504d802152671535b06d380b26d78070559551cbf92df4fc5", size = 33085, upload-time = "2024-07-31T22:39:17.426Z" },
]

[[package]]
name = "mac-alias"
version = "2.2.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/ea/a3/83b50f620d318a98363dc7e701fb94856eaaecc472e23a89ac625697b3ea/mac_alias-2.2.2.tar.gz", hash = "sha256:c99c728eb512e955c11f1a6203a0ffa8883b26549e8afe68804031aa5da856b7", size = 34073, upload-time = "2022-12-06T00:37:47.779Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/39/a1/4136777ed6a56df83e7c748ad28892f0672cbbcdc3b3d15a57df6ba72443/mac_alias-2.2.2-py3-none-any.whl", hash = "sha256:504ab8ac546f35bbd75ad014d6ad977c426660aa721f2cd3acf3dc2f664141bd", size = 21220, upload-time = "2022-12-06T00:37:46.025Z" },
]

[[package]]
name = "makelive"
version = "0.6.2"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "cgmetadata", marker = "sys_platform == 'darwin'" },
    { name = "click", marker = "sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-contacts", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-contacts", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-corelocation", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-corelocation", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "wheel", marker = "sys_platform == 'darwin'" },
    { name = "wurlitzer", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/46/51/a546b6bad0f498fc0723fae6c336c3b73e5e47cffa2b3f83dcd966c084da/makelive-0.6.2.tar.gz", hash = "sha256:df02b715116e462a3e7316517eb91b8f9a1dbad1d6d41352fd562f44a168e858", size = 18723, upload-time = "2024-10-01T20:39:31.887Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/44/1a/d51a6b426adad30c7a8668e11ad976d341c6ecd058a6942495a10a79487f/makelive-0.6.2-py3-none-any.whl", hash = "sha256:1228e90282790f81cc7cdf0a795dc99299c02a545d2225e83210461ce30d1808", size = 11725, upload-time = "2024-10-01T20:39:30.082Z" },
]

[[package]]
name = "mako"
version = "1.2.4"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "markupsafe" },
]
sdist = { url = "https://files.pythonhosted.org/packages/05/5f/2ba6e026d33a0e6ddc1dddf9958677f76f5f80c236bd65309d280b166d3e/Mako-1.2.4.tar.gz", hash = "sha256:d60a3903dc3bb01a18ad6a89cdbe2e4eadc69c0bc8ef1e3773ba53d44c3f7a34", size = 497021, upload-time = "2022-11-15T14:37:51.327Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/03/3b/68690a035ba7347860f1b8c0cde853230ba69ff41df5884ea7d89fe68cd3/Mako-1.2.4-py3-none-any.whl", hash = "sha256:c97c79c018b9165ac9922ae4f32da095ffd3c4e6872b45eded42926deea46818", size = 78672, upload-time = "2022-11-15T14:37:53.675Z" },
]

[[package]]
name = "markdown-it-py"
version = "3.0.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "mdurl" },
]
sdist = { url = "https://files.pythonhosted.org/packages/38/71/3b932df36c1a044d397a1f92d1cf91ee0a503d91e470cbd670aa66b07ed0/markdown-it-py-3.0.0.tar.gz", hash = "sha256:e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb", size = 74596, upload-time = "2023-06-03T06:41:14.443Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl", hash = "sha256:355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1", size = 87528, upload-time = "2023-06-03T06:41:11.019Z" },
]

[[package]]
name = "markupsafe"
version = "3.0.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/b2/97/5d42485e71dfc078108a86d6de8fa46db44a1a9295e89c5d6d4a06e23a62/markupsafe-3.0.2.tar.gz", hash = "sha256:ee55d3edf80167e48ea11a923c7386f4669df67d7994554387f84e7d8b0a2bf0", size = 20537, upload-time = "2024-10-18T15:21:54.129Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/83/0e/67eb10a7ecc77a0c2bbe2b0235765b98d164d81600746914bebada795e97/MarkupSafe-3.0.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ba9527cdd4c926ed0760bc301f6728ef34d841f405abf9d4f959c478421e4efd", size = 14274, upload-time = "2024-10-18T15:21:24.577Z" },
    { url = "https://files.pythonhosted.org/packages/2b/6d/9409f3684d3335375d04e5f05744dfe7e9f120062c9857df4ab490a1031a/MarkupSafe-3.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:f8b3d067f2e40fe93e1ccdd6b2e1d16c43140e76f02fb1319a05cf2b79d99430", size = 12352, upload-time = "2024-10-18T15:21:25.382Z" },
    { url = "https://files.pythonhosted.org/packages/d2/f5/6eadfcd3885ea85fe2a7c128315cc1bb7241e1987443d78c8fe712d03091/MarkupSafe-3.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:569511d3b58c8791ab4c2e1285575265991e6d8f8700c7be0e88f86cb0672094", size = 24122, upload-time = "2024-10-18T15:21:26.199Z" },
    { url = "https://files.pythonhosted.org/packages/0c/91/96cf928db8236f1bfab6ce15ad070dfdd02ed88261c2afafd4b43575e9e9/MarkupSafe-3.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:15ab75ef81add55874e7ab7055e9c397312385bd9ced94920f2802310c930396", size = 23085, upload-time = "2024-10-18T15:21:27.029Z" },
    { url = "https://files.pythonhosted.org/packages/c2/cf/c9d56af24d56ea04daae7ac0940232d31d5a8354f2b457c6d856b2057d69/MarkupSafe-3.0.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f3818cb119498c0678015754eba762e0d61e5b52d34c8b13d770f0719f7b1d79", size = 22978, upload-time = "2024-10-18T15:21:27.846Z" },
    { url = "https://files.pythonhosted.org/packages/2a/9f/8619835cd6a711d6272d62abb78c033bda638fdc54c4e7f4272cf1c0962b/MarkupSafe-3.0.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:cdb82a876c47801bb54a690c5ae105a46b392ac6099881cdfb9f6e95e4014c6a", size = 24208, upload-time = "2024-10-18T15:21:28.744Z" },
    { url = "https://files.pythonhosted.org/packages/f9/bf/176950a1792b2cd2102b8ffeb5133e1ed984547b75db47c25a67d3359f77/MarkupSafe-3.0.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:cabc348d87e913db6ab4aa100f01b08f481097838bdddf7c7a84b7575b7309ca", size = 23357, upload-time = "2024-10-18T15:21:29.545Z" },
    { url = "https://files.pythonhosted.org/packages/ce/4f/9a02c1d335caabe5c4efb90e1b6e8ee944aa245c1aaaab8e8a618987d816/MarkupSafe-3.0.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:444dcda765c8a838eaae23112db52f1efaf750daddb2d9ca300bcae1039adc5c", size = 23344, upload-time = "2024-10-18T15:21:30.366Z" },
    { url = "https://files.pythonhosted.org/packages/ee/55/c271b57db36f748f0e04a759ace9f8f759ccf22b4960c270c78a394f58be/MarkupSafe-3.0.2-cp313-cp313-win32.whl", hash = "sha256:bcf3e58998965654fdaff38e58584d8937aa3096ab5354d493c77d1fdd66d7a1", size = 15101, upload-time = "2024-10-18T15:21:31.207Z" },
    { url = "https://files.pythonhosted.org/packages/29/88/07df22d2dd4df40aba9f3e402e6dc1b8ee86297dddbad4872bd5e7b0094f/MarkupSafe-3.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:e6a2a455bd412959b57a172ce6328d2dd1f01cb2135efda2e4576e8a23fa3b0f", size = 15603, upload-time = "2024-10-18T15:21:32.032Z" },
    { url = "https://files.pythonhosted.org/packages/62/6a/8b89d24db2d32d433dffcd6a8779159da109842434f1dd2f6e71f32f738c/MarkupSafe-3.0.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b5a6b3ada725cea8a5e634536b1b01c30bcdcd7f9c6fff4151548d5bf6b3a36c", size = 14510, upload-time = "2024-10-18T15:21:33.625Z" },
    { url = "https://files.pythonhosted.org/packages/7a/06/a10f955f70a2e5a9bf78d11a161029d278eeacbd35ef806c3fd17b13060d/MarkupSafe-3.0.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:a904af0a6162c73e3edcb969eeeb53a63ceeb5d8cf642fade7d39e7963a22ddb", size = 12486, upload-time = "2024-10-18T15:21:34.611Z" },
    { url = "https://files.pythonhosted.org/packages/34/cf/65d4a571869a1a9078198ca28f39fba5fbb910f952f9dbc5220afff9f5e6/MarkupSafe-3.0.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4aa4e5faecf353ed117801a068ebab7b7e09ffb6e1d5e412dc852e0da018126c", size = 25480, upload-time = "2024-10-18T15:21:35.398Z" },
    { url = "https://files.pythonhosted.org/packages/0c/e3/90e9651924c430b885468b56b3d597cabf6d72be4b24a0acd1fa0e12af67/MarkupSafe-3.0.2-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c0ef13eaeee5b615fb07c9a7dadb38eac06a0608b41570d8ade51c56539e509d", size = 23914, upload-time = "2024-10-18T15:21:36.231Z" },
    { url = "https://files.pythonhosted.org/packages/66/8c/6c7cf61f95d63bb866db39085150df1f2a5bd3335298f14a66b48e92659c/MarkupSafe-3.0.2-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d16a81a06776313e817c951135cf7340a3e91e8c1ff2fac444cfd75fffa04afe", size = 23796, upload-time = "2024-10-18T15:21:37.073Z" },
    { url = "https://files.pythonhosted.org/packages/bb/35/cbe9238ec3f47ac9a7c8b3df7a808e7cb50fe149dc7039f5f454b3fba218/MarkupSafe-3.0.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:6381026f158fdb7c72a168278597a5e3a5222e83ea18f543112b2662a9b699c5", size = 25473, upload-time = "2024-10-18T15:21:37.932Z" },
    { url = "https://files.pythonhosted.org/packages/e6/32/7621a4382488aa283cc05e8984a9c219abad3bca087be9ec77e89939ded9/MarkupSafe-3.0.2-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:3d79d162e7be8f996986c064d1c7c817f6df3a77fe3d6859f6f9e7be4b8c213a", size = 24114, upload-time = "2024-10-18T15:21:39.799Z" },
    { url = "https://files.pythonhosted.org/packages/0d/80/0985960e4b89922cb5a0bac0ed39c5b96cbc1a536a99f30e8c220a996ed9/MarkupSafe-3.0.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:131a3c7689c85f5ad20f9f6fb1b866f402c445b220c19fe4308c0b147ccd2ad9", size = 24098, upload-time = "2024-10-18T15:21:40.813Z" },
    { url = "https://files.pythonhosted.org/packages/82/78/fedb03c7d5380df2427038ec8d973587e90561b2d90cd472ce9254cf348b/MarkupSafe-3.0.2-cp313-cp313t-win32.whl", hash = "sha256:ba8062ed2cf21c07a9e295d5b8a2a5ce678b913b45fdf68c32d95d6c1291e0b6", size = 15208, upload-time = "2024-10-18T15:21:41.814Z" },
    { url = "https://files.pythonhosted.org/packages/4f/65/6079a46068dfceaeabb5dcad6d674f5f5c61a6fa5673746f42a9f4c233b3/MarkupSafe-3.0.2-cp313-cp313t-win_amd64.whl", hash = "sha256:e444a31f8db13eb18ada366ab3cf45fd4b31e4db1236a4448f68778c1d1a5a2f", size = 15739, upload-time = "2024-10-18T15:21:42.784Z" },
]

[[package]]
name = "mccabe"
version = "0.7.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/e7/ff/0ffefdcac38932a54d2b5eed4e0ba8a408f215002cd178ad1df0f2806ff8/mccabe-0.7.0.tar.gz", hash = "sha256:348e0240c33b60bbdf4e523192ef919f28cb2c3d7d5c7794f74009290f236325", size = 9658, upload-time = "2022-01-24T01:14:51.113Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl", hash = "sha256:6c2d30ab6be0e4a46919781807b4f0d834ebdd6c6e3dca0bda5a15f863427b6e", size = 7350, upload-time = "2022-01-24T01:14:49.62Z" },
]

[[package]]
name = "mdurl"
version = "0.1.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d6/54/cfe61301667036ec958cb99bd3efefba235e65cdeb9c84d24a8293ba1d90/mdurl-0.1.2.tar.gz", hash = "sha256:bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba", size = 8729, upload-time = "2022-08-14T12:40:10.846Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl", hash = "sha256:84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8", size = 9979, upload-time = "2022-08-14T12:40:09.779Z" },
]

[[package]]
name = "media-archive-rr"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "osxphotos" },
]

[package.dev-dependencies]
dev = [
    { name = "black" },
    { name = "flake8" },
    { name = "mypy" },
    { name = "pytest" },
]

[package.metadata]
requires-dist = [{ name = "osxphotos", specifier = ">=0.71.0" }]

[package.metadata.requires-dev]
dev = [
    { name = "black", specifier = ">=25.1.0" },
    { name = "flake8", specifier = ">=7.2.0" },
    { name = "mypy", specifier = ">=1.16.0" },
    { name = "pytest", specifier = ">=8.4.0" },
]

[[package]]
name = "more-itertools"
version = "8.14.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/c7/0c/fad24ca2c9283abc45a32b3bfc2a247376795683449f595ff1280c171396/more-itertools-8.14.0.tar.gz", hash = "sha256:c09443cd3d5438b8dafccd867a6bc1cb0894389e90cb53d227456b0b0bccb750", size = 102642, upload-time = "2022-08-09T14:04:04.966Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0b/ff/1ad78678bee731ae5414ea5e97396b3f91de32186028daa614d322ac5a8b/more_itertools-8.14.0-py3-none-any.whl", hash = "sha256:1bc4f91ee5b1b31ac7ceacc17c09befe6a40a503907baf9c839c229b5095cfd2", size = 52193, upload-time = "2022-08-09T14:04:02.452Z" },
]

[[package]]
name = "mypy"
version = "1.16.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "mypy-extensions" },
    { name = "pathspec" },
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d4/38/13c2f1abae94d5ea0354e146b95a1be9b2137a0d506728e0da037c4276f6/mypy-1.16.0.tar.gz", hash = "sha256:84b94283f817e2aa6350a14b4a8fb2a35a53c286f97c9d30f53b63620e7af8ab", size = 3323139, upload-time = "2025-05-29T13:46:12.532Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/97/9c/ca03bdbefbaa03b264b9318a98950a9c683e06472226b55472f96ebbc53d/mypy-1.16.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:a9e056237c89f1587a3be1a3a70a06a698d25e2479b9a2f57325ddaaffc3567b", size = 11059753, upload-time = "2025-05-29T13:18:18.167Z" },
    { url = "https://files.pythonhosted.org/packages/36/92/79a969b8302cfe316027c88f7dc6fee70129490a370b3f6eb11d777749d0/mypy-1.16.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:0b07e107affb9ee6ce1f342c07f51552d126c32cd62955f59a7db94a51ad12c0", size = 10073338, upload-time = "2025-05-29T13:19:48.079Z" },
    { url = "https://files.pythonhosted.org/packages/14/9b/a943f09319167da0552d5cd722104096a9c99270719b1afeea60d11610aa/mypy-1.16.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:c6fb60cbd85dc65d4d63d37cb5c86f4e3a301ec605f606ae3a9173e5cf34997b", size = 11827764, upload-time = "2025-05-29T13:46:04.47Z" },
    { url = "https://files.pythonhosted.org/packages/ec/64/ff75e71c65a0cb6ee737287c7913ea155845a556c64144c65b811afdb9c7/mypy-1.16.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:a7e32297a437cc915599e0578fa6bc68ae6a8dc059c9e009c628e1c47f91495d", size = 12701356, upload-time = "2025-05-29T13:35:13.553Z" },
    { url = "https://files.pythonhosted.org/packages/0a/ad/0e93c18987a1182c350f7a5fab70550852f9fabe30ecb63bfbe51b602074/mypy-1.16.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:afe420c9380ccec31e744e8baff0d406c846683681025db3531b32db56962d52", size = 12900745, upload-time = "2025-05-29T13:17:24.409Z" },
    { url = "https://files.pythonhosted.org/packages/28/5d/036c278d7a013e97e33f08c047fe5583ab4f1fc47c9a49f985f1cdd2a2d7/mypy-1.16.0-cp313-cp313-win_amd64.whl", hash = "sha256:55f9076c6ce55dd3f8cd0c6fff26a008ca8e5131b89d5ba6d86bd3f47e736eeb", size = 9572200, upload-time = "2025-05-29T13:33:44.92Z" },
    { url = "https://files.pythonhosted.org/packages/99/a3/6ed10530dec8e0fdc890d81361260c9ef1f5e5c217ad8c9b21ecb2b8366b/mypy-1.16.0-py3-none-any.whl", hash = "sha256:29e1499864a3888bca5c1542f2d7232c6e586295183320caa95758fc84034031", size = 2265773, upload-time = "2025-05-29T13:35:18.762Z" },
]

[[package]]
name = "mypy-extensions"
version = "1.1.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a2/6e/371856a3fb9d31ca8dac321cda606860fa4548858c0cc45d9d1d4ca2628b/mypy_extensions-1.1.0.tar.gz", hash = "sha256:52e68efc3284861e772bbcd66823fde5ae21fd2fdb51c62a211403730b916558", size = 6343, upload-time = "2025-04-22T14:54:24.164Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl", hash = "sha256:1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505", size = 4963, upload-time = "2025-04-22T14:54:22.983Z" },
]

[[package]]
name = "objexplore"
version = "1.6.3"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "blessed" },
    { name = "rich" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3b/a4/a3d92448b0319782a8578fcba0a00cc3e3335224fcf753d48f00ae948abf/objexplore-1.6.3.tar.gz", hash = "sha256:8298ea17d66084b3ad6e04239da19d7051f2a80669b5c0118f7fb9b854ba6370", size = 19434, upload-time = "2022-02-19T20:50:42.43Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/62/79/f66a0d66ea0d814695127c163c690fc209f6cc062e729ce9af5eb1cf3e7f/objexplore-1.6.3-py3-none-any.whl", hash = "sha256:e9427dddb7cf35c51d53d2191c146031e96efb98efdf47670044ba91ac1f9c69", size = 22481, upload-time = "2022-02-19T20:50:40.62Z" },
]

[[package]]
name = "osxmetadata"
version = "1.4.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "bitstring", marker = "sys_platform == 'darwin'" },
    { name = "click", marker = "sys_platform == 'darwin'" },
    { name = "py-applescript", marker = "sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coremedia", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coremedia", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreservices", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreservices", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-scriptingbridge", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-scriptingbridge", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "wheel", marker = "sys_platform == 'darwin'" },
    { name = "xattr", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a5/56/2178e16de4a36e067f12872fdc3f0b574a795bdcd1ca956349cea9fc9ce0/osxmetadata-1.4.0.tar.gz", hash = "sha256:979bf075ab5e5b0e96287df88d8bf7bb9806d858d1b2a52b5e4b72d89e50a6f3", size = 2452955, upload-time = "2024-11-17T01:18:33.088Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2d/7d/f149d0ae80443116a93774efe12458bd709fce08a3b5d94b99e3a2619c4b/osxmetadata-1.4.0-py3-none-any.whl", hash = "sha256:c4d98b41c9fab440f905d58037975608c014291777f09c27c0202981b5f6c178", size = 56258, upload-time = "2024-11-17T01:18:11.796Z" },
]

[[package]]
name = "osxphotos"
version = "0.71.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "bitmath" },
    { name = "bpylist2" },
    { name = "cgmetadata", marker = "sys_platform == 'darwin'" },
    { name = "click" },
    { name = "mac-alias", marker = "sys_platform == 'darwin'" },
    { name = "makelive", marker = "sys_platform == 'darwin'" },
    { name = "mako" },
    { name = "more-itertools" },
    { name = "objexplore" },
    { name = "osxmetadata", marker = "sys_platform == 'darwin'" },
    { name = "packaging" },
    { name = "pathvalidate" },
    { name = "photoscript", marker = "sys_platform == 'darwin'" },
    { name = "pip" },
    { name = "ptpython" },
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-avfoundation", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreservices", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreservices", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-metal", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-metal", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-photos", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-photos", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-vision", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-vision", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pytimeparse2" },
    { name = "pyyaml" },
    { name = "requests" },
    { name = "rich" },
    { name = "rich-theme-manager" },
    { name = "shortuuid" },
    { name = "strpdatetime" },
    { name = "tenacity" },
    { name = "textx" },
    { name = "toml" },
    { name = "tzdata" },
    { name = "utitools" },
    { name = "whenever" },
    { name = "wrapt" },
    { name = "wurlitzer" },
    { name = "xdg-base-dirs" },
]
sdist = { url = "https://files.pythonhosted.org/packages/20/90/6b1b7ed9872f30c0df3ae67dd387f03a2e17caefabb6baaf9aefc21c06b6/osxphotos-0.71.0.tar.gz", hash = "sha256:7d8ae7af09024537a2b66fa16bdce5c20e77b5e2cbd0a1e2033287c3ff121d5b", size = 2256888, upload-time = "2025-06-07T18:51:17.666Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2c/ac/f5b7055680f92efb3001e1eb3ba2a2661e4af6137b4d503b045a68504ea2/osxphotos-0.71.0-py3-none-any.whl", hash = "sha256:5e07a413ca7d8b64d85d73e851751a5fd24a1f907679c0de59a7a2a5b64852f9", size = 1919557, upload-time = "2025-06-07T18:51:15.498Z" },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a1/d4/1fc4078c65507b51b96ca8f8c3ba19e6a61c8253c72794544580a7b6c24d/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f", size = 165727, upload-time = "2025-04-19T11:48:59.673Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484", size = 66469, upload-time = "2025-04-19T11:48:57.875Z" },
]

[[package]]
name = "parso"
version = "0.8.4"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/66/94/68e2e17afaa9169cf6412ab0f28623903be73d1b32e208d9e8e541bb086d/parso-0.8.4.tar.gz", hash = "sha256:eb3a7b58240fb99099a345571deecc0f9540ea5f4dd2fe14c2a99d6b281ab92d", size = 400609, upload-time = "2024-04-05T09:43:55.897Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c6/ac/dac4a63f978e4dcb3c6d3a78c4d8e0192a113d288502a1216950c41b1027/parso-0.8.4-py2.py3-none-any.whl", hash = "sha256:a418670a20291dacd2dddc80c377c5c3791378ee1e8d12bffc35420643d43f18", size = 103650, upload-time = "2024-04-05T09:43:53.299Z" },
]

[[package]]
name = "pathspec"
version = "0.12.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/ca/bc/f35b8446f4531a7cb215605d100cd88b7ac6f44ab3fc94870c120ab3adbf/pathspec-0.12.1.tar.gz", hash = "sha256:a482d51503a1ab33b1c67a6c3813a26953dbdc71c31dacaef9a838c4e29f5712", size = 51043, upload-time = "2023-12-10T22:30:45Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl", hash = "sha256:a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08", size = 31191, upload-time = "2023-12-10T22:30:43.14Z" },
]

[[package]]
name = "pathvalidate"
version = "3.2.3"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/92/87/c7a2f51cc62df0495acb0ed2533a7c74cc895e569a1b020ee5f6e9fa4e21/pathvalidate-3.2.3.tar.gz", hash = "sha256:59b5b9278e30382d6d213497623043ebe63f10e29055be4419a9c04c721739cb", size = 61717, upload-time = "2025-01-03T14:06:42.789Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/50/14/c5a0e1a947909810fc4c043b84cac472b70e438148d34f5393be1bac663f/pathvalidate-3.2.3-py3-none-any.whl", hash = "sha256:5eaf0562e345d4b6d0c0239d0f690c3bd84d2a9a3c4c73b99ea667401b27bee1", size = 24130, upload-time = "2025-01-03T14:06:39.568Z" },
]

[[package]]
name = "photoscript"
version = "0.4.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "py-applescript", marker = "sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/cb/cb/39b1db76fdfa4439de0c5e90fc20ffdeb9dedd01e62b0dce56ee954caf81/photoscript-0.4.0.tar.gz", hash = "sha256:5b00d663b7b2c2450d54fbba8a9d4864be5beb9396eac47e1dd3aeaecdc34366", size = 26315, upload-time = "2024-11-11T14:11:54.698Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9a/f1/2d9e5f687a88b025bc88b945ed290989343906d509740f2bf8627319534b/photoscript-0.4.0-py3-none-any.whl", hash = "sha256:051a9fb96e948e0ca6f195b7fdc631f80e02d2864ba2acff70d5bee706a44d75", size = 24282, upload-time = "2024-11-11T14:11:53.005Z" },
]

[[package]]
name = "pip"
version = "25.1.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/59/de/241caa0ca606f2ec5fe0c1f4261b0465df78d786a38da693864a116c37f4/pip-25.1.1.tar.gz", hash = "sha256:3de45d411d308d5054c2168185d8da7f9a2cd753dbac8acbfa88a8909ecd9077", size = 1940155, upload-time = "2025-05-02T15:14:02.057Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/29/a2/d40fb2460e883eca5199c62cfc2463fd261f760556ae6290f88488c362c0/pip-25.1.1-py3-none-any.whl", hash = "sha256:2913a38a2abf4ea6b64ab507bd9e967f3b53dc1ede74b01b0931e1ce548751af", size = 1825227, upload-time = "2025-05-02T15:13:59.102Z" },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/fe/8b/3c73abc9c759ecd3f1f7ceff6685840859e8070c4d947c93fae71f6a0bf2/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc", size = 21362, upload-time = "2025-05-07T22:47:42.121Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4", size = 18567, upload-time = "2025-05-07T22:47:40.376Z" },
]

[[package]]
name = "pluggy"
version = "1.6.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/f9/e2/3e91f31a7d2b083fe6ef3fa267035b518369d9511ffab804f839851d2779/pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3", size = 69412, upload-time = "2025-05-15T12:30:07.975Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746", size = 20538, upload-time = "2025-05-15T12:30:06.134Z" },
]

[[package]]
name = "prompt-toolkit"
version = "3.0.51"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "wcwidth" },
]
sdist = { url = "https://files.pythonhosted.org/packages/bb/6e/9d084c929dfe9e3bfe0c6a47e31f78a25c54627d64a66e884a8bf5474f1c/prompt_toolkit-3.0.51.tar.gz", hash = "sha256:931a162e3b27fc90c86f1b48bb1fb2c528c2761475e57c9c06de13311c7b54ed", size = 428940, upload-time = "2025-04-15T09:18:47.731Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ce/4f/5249960887b1fbe561d9ff265496d170b55a735b76724f10ef19f9e40716/prompt_toolkit-3.0.51-py3-none-any.whl", hash = "sha256:52742911fde84e2d423e2f9a4cf1de7d7ac4e51958f648d9540e0fb8db077b07", size = 387810, upload-time = "2025-04-15T09:18:44.753Z" },
]

[[package]]
name = "ptpython"
version = "3.0.30"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "appdirs" },
    { name = "jedi" },
    { name = "prompt-toolkit" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c9/ce/4441ac40762c73d74b48088a7311e368d28beec92602d66e632a59792a93/ptpython-3.0.30.tar.gz", hash = "sha256:51a07f9b8ebf8435a5aaeb22831cca4a52e87029771a2637df2763c79d3d8776", size = 72812, upload-time = "2025-04-15T09:26:37.534Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/05/15/77dfd9a52fa6c87b50232b246df0cfacacc0665c95ebe4a517cc994819f0/ptpython-3.0.30-py3-none-any.whl", hash = "sha256:bec3045f0285ac817c902ef98d6ece31d3e00a4604ef3fdde07d365c78bde23c", size = 67249, upload-time = "2025-04-15T09:26:35.693Z" },
]

[[package]]
name = "py-applescript"
version = "1.0.3"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptkit", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-applescriptobjc", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b2/13/781639401dd0e6fc11b2b6d4999ec8e951b50df2600eebee8e929b009da1/py-applescript-1.0.3.tar.gz", hash = "sha256:fa22c955fc25b3d24e03e66825b36a721897ec0d9b6ce185a4d177e2d1ecfa6b", size = 24925, upload-time = "2022-01-23T05:39:23.427Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9b/f6/fed1fb7eb1603ad50accbb5c4e1f0c5f8f7d164acd2619f062d20c2a7858/py_applescript-1.0.3-py3-none-any.whl", hash = "sha256:eb21dfc6e845a40be602372a5c3d6317f7ad3c999c2c355a00d1a19bdb1d57c0", size = 22392, upload-time = "2022-01-23T05:39:35.966Z" },
]

[[package]]
name = "pycodestyle"
version = "2.13.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/04/6e/1f4a62078e4d95d82367f24e685aef3a672abfd27d1a868068fed4ed2254/pycodestyle-2.13.0.tar.gz", hash = "sha256:c8415bf09abe81d9c7f872502a6eee881fbe85d8763dd5b9924bb0a01d67efae", size = 39312, upload-time = "2025-03-29T17:33:30.669Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/07/be/b00116df1bfb3e0bb5b45e29d604799f7b91dd861637e4d448b4e09e6a3e/pycodestyle-2.13.0-py2.py3-none-any.whl", hash = "sha256:35863c5974a271c7a726ed228a14a4f6daf49df369d8c50cd9a6f58a5e143ba9", size = 31424, upload-time = "2025-03-29T17:33:29.405Z" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/1d/b2/31537cf4b1ca988837256c910a668b553fceb8f069bedc4b1c826024b52c/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6", size = 172736, upload-time = "2024-03-30T13:22:22.564Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc", size = 117552, upload-time = "2024-03-30T13:22:20.476Z" },
]

[[package]]
name = "pyflakes"
version = "3.3.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/af/cc/1df338bd7ed1fa7c317081dcf29bf2f01266603b301e6858856d346a12b3/pyflakes-3.3.2.tar.gz", hash = "sha256:6dfd61d87b97fba5dcfaaf781171ac16be16453be6d816147989e7f6e6a9576b", size = 64175, upload-time = "2025-03-31T13:21:20.34Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/15/40/b293a4fa769f3b02ab9e387c707c4cbdc34f073f945de0386107d4e669e6/pyflakes-3.3.2-py2.py3-none-any.whl", hash = "sha256:5039c8339cbb1944045f4ee5466908906180f13cc99cc9949348d10f82a5c32a", size = 63164, upload-time = "2025-03-31T13:21:18.503Z" },
]

[[package]]
name = "pygments"
version = "2.19.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/7c/2d/c3338d48ea6cc0feb8446d8e6937e1408088a72a39937982cc6111d17f84/pygments-2.19.1.tar.gz", hash = "sha256:61c16d2a8576dc0649d9f39e089b5f02bcd27fba10d8fb4dcc28173f7a45151f", size = 4968581, upload-time = "2025-01-06T17:26:30.443Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/0b/9fcc47d19c48b59121088dd6da2488a49d5f72dacf8262e2790a1d2c7d15/pygments-2.19.1-py3-none-any.whl", hash = "sha256:9ea1544ad55cecf4b8242fab6dd35a93bbce657034b0611ee383099054ab6d8c", size = 1225293, upload-time = "2025-01-06T17:26:25.553Z" },
]

[[package]]
name = "pyobjc-core"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
sdist = { url = "https://files.pythonhosted.org/packages/48/d9/a13566ce8914746557b9e8637a5abe1caae86ed202b0fb072029626b8bb1/pyobjc-core-9.2.tar.gz", hash = "sha256:d734b9291fec91ff4e3ae38b9c6839debf02b79c07314476e87da8e90b2c68c3", size = 923758, upload-time = "2023-06-07T08:33:03.348Z" }

[[package]]
name = "pyobjc-core"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
sdist = { url = "https://files.pythonhosted.org/packages/5d/07/2b3d63c0349fe4cf34d787a52a22faa156225808db2d1531fe58fabd779d/pyobjc_core-10.3.2.tar.gz", hash = "sha256:dbf1475d864ce594288ce03e94e3a98dc7f0e4639971eb1e312bdf6661c21e0e", size = 935182, upload-time = "2024-11-30T15:24:44.294Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/08/27/e7b8240c116cd8231ac33daaf982e36f77be33cf5448bbc568ce17371a79/pyobjc_core-10.3.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:76b8b911d94501dac89821df349b1860bb770dce102a1a293f524b5b09dd9462", size = 827885, upload-time = "2024-11-30T12:50:41.942Z" },
    { url = "https://files.pythonhosted.org/packages/de/a3/897cc31fca822a4df4ece31e4369dd9eae35bcb0b535fc9c7c21924268ba/pyobjc_core-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:8c6288fdb210b64115760a4504efbc4daffdc390d309e9318eb0e3e3b78d2828", size = 837794, upload-time = "2024-11-30T12:51:05.748Z" },
]

[[package]]
name = "pyobjc-framework-applescriptkit"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/62/e2/1d566374e942c76aeb01754ed63d6132d85ebbe32504809e9a102ec6cec3/pyobjc-framework-AppleScriptKit-9.2.tar.gz", hash = "sha256:4215952cc18b810ca314a59f1a0b9ddbe5d9c7f2d4cc6a866cad570ea96b7496", size = 11483, upload-time = "2023-06-07T08:38:10.456Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c6/29/0152a476db6c9cd55b54b6c6f3e8b855fab4e698e8b8428b78757831fb7e/pyobjc_framework_AppleScriptKit-9.2-py2.py3-none-any.whl", hash = "sha256:e7be610629371efc96b2daf8b83fee814251ce0845d8cfd4cc458a69bd67e6fb", size = 3838, upload-time = "2023-06-07T07:40:53.256Z" },
]

[[package]]
name = "pyobjc-framework-applescriptkit"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1a/78/5abe58d1698dfacc0e5ab719aa2cd93879230e45b9387bcc3b0bb91040d3/pyobjc_framework_applescriptkit-10.3.2.tar.gz", hash = "sha256:a4d74fc6b28d1ff7d39b60c9e0c2d5d1baf575ade6e6d1ea06ed077facec47db", size = 12102, upload-time = "2024-11-30T15:26:39.268Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7b/7b/2abb01be55d4633ecae77af4d85077e7825452ece51daf4cd0cde0d8ae49/pyobjc_framework_AppleScriptKit-10.3.2-py2.py3-none-any.whl", hash = "sha256:a970410ece8004a912918eed3173b2771c857fb8eb3b61f8d796d3e0e0b759d6", size = 3931, upload-time = "2024-11-30T13:00:52.866Z" },
    { url = "https://files.pythonhosted.org/packages/50/bb/2e8ff9f8d4b72ba43e3e0e45f9aa4ce8d02352e8dd4a6321bfc61371ec21/pyobjc_framework_AppleScriptKit-10.3.2-py3-none-any.whl", hash = "sha256:38e7b573d3d5b3773d8a7f2189cad2378d32353d597dcd6342e2419dd6310c0e", size = 3926, upload-time = "2024-11-30T13:01:32.795Z" },
]

[[package]]
name = "pyobjc-framework-applescriptobjc"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3c/c0/ab3922a01a6649a56c8f1fddbf7036a7212ee77da880501278e9390e8675/pyobjc-framework-AppleScriptObjC-9.2.tar.gz", hash = "sha256:f6216bf110072e70938e1efa1a058d48452a9d9251911bee26102bad4091a051", size = 11502, upload-time = "2023-06-07T08:38:15.577Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/85/d8/ef8a1c0768c72a87c76f9277456cb0c4c55fe9afbc92637cd0f3b4054670/pyobjc_framework_AppleScriptObjC-9.2-py2.py3-none-any.whl", hash = "sha256:66c1fa4239c344a1750afd5beaf6550136d777c3073c47303c8ed224e2517282", size = 3932, upload-time = "2023-06-07T07:40:54.938Z" },
]

[[package]]
name = "pyobjc-framework-applescriptobjc"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f0/8b/d720f671b21a07a8d1815c54ce4e8f313f73ea645a82faa8331a2a05d9c2/pyobjc_framework_applescriptobjc-10.3.2.tar.gz", hash = "sha256:6af16cab0fe4e2d50775e67501bcecae1c5acdba7ed560eba38e5f8e3c8ac38c", size = 12166, upload-time = "2024-11-30T15:26:41.128Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fb/46/eff05f226e5834c9f24cc96b12e09d0da08165264f1fde813ba715ca2f6e/pyobjc_framework_AppleScriptObjC-10.3.2-py2.py3-none-any.whl", hash = "sha256:a932ffdcf6a5b5ac884666bb0ae2a8075528f489b0b5aa4336fc22e6f011664e", size = 4030, upload-time = "2024-11-30T13:01:50.824Z" },
    { url = "https://files.pythonhosted.org/packages/81/6b/b00195e4431651ffdb313d35da3c3f27a8b2558a3219bb00221500b75b59/pyobjc_framework_AppleScriptObjC-10.3.2-py3-none-any.whl", hash = "sha256:e0a0496fc05e7b23d6030d9dfcd706167ad05e7032f69117bc0136234eebc12e", size = 4026, upload-time = "2024-11-30T13:01:53.444Z" },
]

[[package]]
name = "pyobjc-framework-avfoundation"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreaudio", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coremedia", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/9f/0c/f91c9bf84a1f175685c65e3d2b4893f88f41cf12b6d5da03cf00b627e34f/pyobjc-framework-AVFoundation-9.2.tar.gz", hash = "sha256:ecf0db71abad6baf127e454e9995adf372249ec6b99e3ede8b6149460ee39c35", size = 785336, upload-time = "2023-06-07T08:36:50.871Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/39/f0/01ead0d2aec1d6f9eae42c485d38130fb83fcd214dcc654b35bf8f644deb/pyobjc_framework_AVFoundation-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:96b11c7dfca6d922754549be6d7c1e7bcb1e510821fe7a47134b035a8a6feb43", size = 61873, upload-time = "2023-06-07T07:39:04.522Z" },
    { url = "https://files.pythonhosted.org/packages/17/4c/d544f51fb3e58fa6f84001e1f66e0553596685a96e61792d21d2cc54083f/pyobjc_framework_AVFoundation-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:7fded6b70088240418319769cf6ec854de85a9db7db3d97d9829e7aaf84bf121", size = 49763, upload-time = "2023-06-07T07:39:20.976Z" },
    { url = "https://files.pythonhosted.org/packages/c6/da/6d51eddfba9e1b7dbd209eaddac5eee0b2bacecc52b50df6e32ae6407142/pyobjc_framework_AVFoundation-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:70a4504288a2ca9dccd43cbfd38a5a4c7096915b1c522cf4adb7649c071463bb", size = 61974, upload-time = "2023-06-07T07:39:40.871Z" },
]

[[package]]
name = "pyobjc-framework-avfoundation"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreaudio", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coremedia", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/db/8d/8a78df7d0ccbf7e8f7a80692f7891895b323334bde2781a6018452c92eb1/pyobjc_framework_avfoundation-10.3.2.tar.gz", hash = "sha256:e4baa1cb8d63c2b366f90341dee54a646004ad02d4b01119c79904cb869e7a6a", size = 695532, upload-time = "2024-11-30T15:29:09.909Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5d/39/0f28fe64c5211da9d2d6484fe7bd54f558575bb8b22af358345d86765e30/pyobjc_framework_AVFoundation-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e64797d5ec23d8eb5162e14107c1979244c7a09cce2f7ed3bb3fbbb45ba1fec7", size = 67618, upload-time = "2024-11-30T12:52:30.736Z" },
    { url = "https://files.pythonhosted.org/packages/4c/2a/f4710ceee7ff485d5e63893fd97e2cfebbef006c593e2f49cbd507cdca21/pyobjc_framework_AVFoundation-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:1a357b4264909c9f29a467d6706e12a822c1d6b9b9b274dd5892500cc9265681", size = 66809, upload-time = "2024-11-30T12:52:33.828Z" },
    { url = "https://files.pythonhosted.org/packages/49/29/30f7a6718e40d027ee9aff93fa5ea63f2a8c8367a8ff359fb682380f6ed7/pyobjc_framework_AVFoundation-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:cf41bd0c3e1269d892bd112c893507f8a3991244a9217d103dc2beb4366a8769", size = 66742, upload-time = "2024-11-30T12:52:54.673Z" },
    { url = "https://files.pythonhosted.org/packages/63/62/9ada601d16d4cba65076aae40d869a16e4ea07755f989c84723cd12e5b63/pyobjc_framework_AVFoundation-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:4c257341a4baeb10371e4bd8eaa89a077a1fb8095a0ebed15117b7cb424e0b57", size = 54727, upload-time = "2024-11-30T12:53:39.767Z" },
    { url = "https://files.pythonhosted.org/packages/73/18/b76ec3753432034f7f290c2894f812302d037b831304f7ef4c932e70ce34/pyobjc_framework_AVFoundation-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:50a4e245d5e65f525e23c9bda78ccfbaf3492b661cb006f2c9b6f0d9c9d368f8", size = 66874, upload-time = "2024-11-30T12:54:00.202Z" },
]

[[package]]
name = "pyobjc-framework-cocoa"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/38/91/c54fdffda6d7cfad67ff617f19001163658d50bc72376d1584e691cf4895/pyobjc-framework-Cocoa-9.2.tar.gz", hash = "sha256:efd78080872d8c8de6c2b97e0e4eac99d6203a5d1637aa135d071d464eb2db53", size = 6170134, upload-time = "2023-06-07T09:14:13.129Z" }

[[package]]
name = "pyobjc-framework-cocoa"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/39/41/4f09a5e9a6769b4dafb293ea597ed693cc0def0e07867ad0a42664f530b6/pyobjc_framework_cocoa-10.3.2.tar.gz", hash = "sha256:673968e5435845bef969bfe374f31a1a6dc660c98608d2b84d5cae6eafa5c39d", size = 4942530, upload-time = "2024-11-30T15:30:27.244Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4e/c4/bccb4c05422170c0afccf6ebbdcc7551f7ddd03d2f7a65498d02cb179993/pyobjc_framework_Cocoa-10.3.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f1161b5713f9b9934c12649d73a6749617172e240f9431eff9e22175262fdfda", size = 381878, upload-time = "2024-11-30T13:18:26.24Z" },
    { url = "https://files.pythonhosted.org/packages/25/ec/68657a633512edb84ecb1ff47a067a81028d6f027aa923e806400d2f8a26/pyobjc_framework_Cocoa-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:08e48b9ee4eb393447b2b781d16663b954bd10a26927df74f92e924c05568d89", size = 384925, upload-time = "2024-11-30T13:18:28.171Z" },
]

[[package]]
name = "pyobjc-framework-contacts"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/b6/e0/3af081a26f545c39e79101653f80047965266ab9ccc8b432130801a00dc7/pyobjc-framework-Contacts-9.2.tar.gz", hash = "sha256:1e5ae6a612cae95c010eb5ccf6c2d70a97faf25c7d62b4146fc51424d7fc4b60", size = 68082, upload-time = "2023-06-07T09:14:50.935Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b4/3f/a9699f27e17baae958ce32aaf6ea9fcebfa29917d56568f45f9395ac1ba8/pyobjc_framework_Contacts-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:ba3f55ccf5d3588b7d5765d7ebea9ddd850c16d02668ee90129c7d4084bf24a1", size = 12691, upload-time = "2023-06-07T07:56:28.058Z" },
    { url = "https://files.pythonhosted.org/packages/38/36/875fe42139e728147b85805f23fb42923c10c947243330cc022b00150aa4/pyobjc_framework_Contacts-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:002ff8d2131e354138eae39d1a3823770cab8294d93ddbaa2f0776fdb91c11e5", size = 9128, upload-time = "2023-06-07T07:56:32.443Z" },
    { url = "https://files.pythonhosted.org/packages/e4/39/6fef7a44b15a89e31d88c57de76ba20c798a4e22a74e50334c1148bb4eef/pyobjc_framework_Contacts-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:bcf6c6760c09b012da2b01f9b7ed1cf8716c403e3561d2e9188b2e1b49dec8fc", size = 12790, upload-time = "2023-06-07T07:56:37.919Z" },
]

[[package]]
name = "pyobjc-framework-contacts"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/d5/94/14eb1abc06a88d1621eeb39784a9a1346f417c8584d37d767875c50bf54f/pyobjc_framework_contacts-10.3.2.tar.gz", hash = "sha256:f912a1bbd3cee3d8af740e02abc083828604265394871c2c166bc9c1de3130ce", size = 68818, upload-time = "2024-11-30T15:31:14.903Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/e5/d5/89af3f415f280dbba19ec2d8095e576932e7d301b2c734836f3724a62c1b/pyobjc_framework_Contacts-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:f88170a392c1d6a75b99d40fb4e95987ec2e8fb3a78d43fdfe7f552316b741ad", size = 12788, upload-time = "2024-11-30T13:20:41.633Z" },
    { url = "https://files.pythonhosted.org/packages/7b/36/f20ab836c3d1ca92ad064258387dd96598a47f9328056b10373aed4a3232/pyobjc_framework_Contacts-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:8723c5e472b6fbe7cbdee5c999ffd32b4d93900cdb47f156d9304abe3f0068c1", size = 12037, upload-time = "2024-11-30T13:20:45.352Z" },
    { url = "https://files.pythonhosted.org/packages/81/3b/3217719eae52514bd040a2123774b2023b06765cada2ce10ae727f91c788/pyobjc_framework_Contacts-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:86b7bc80e0b82665eb6e74aecd8efcfe2bb8678bf34097133a6b1a34fb200e93", size = 11936, upload-time = "2024-11-30T13:21:02.606Z" },
    { url = "https://files.pythonhosted.org/packages/4d/7e/3e979ec7cfdbddaf33762b129d6c6ef772ec88b71fad2603cef723912969/pyobjc_framework_Contacts-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:528164fc9c9f15e5fc51a8c1d89bc211d93b3cf5ee659d492d7fb414f265f1e9", size = 9246, upload-time = "2024-11-30T13:21:34.202Z" },
    { url = "https://files.pythonhosted.org/packages/77/f3/776bba456e4f3703e94cd50849c8f432b6e3149879e76eec4a024fabd530/pyobjc_framework_Contacts-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:8eee545f6605dc44fe35dcb8018b530d05ccbb0fa6fda61a0df4e13666c9377d", size = 12499, upload-time = "2024-11-30T13:21:35.671Z" },
]

[[package]]
name = "pyobjc-framework-coreaudio"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/51/43/d15c986e635066db113bd6a1f6d99857bd88ac46bf70f619b2cdff8362f3/pyobjc-framework-CoreAudio-9.2.tar.gz", hash = "sha256:7b8e32e073b04896850c971a9e4c5afaf9f3343b8e525bb23f50a360c587cf87", size = 123210, upload-time = "2023-06-07T09:15:34.64Z" }

[[package]]
name = "pyobjc-framework-coreaudio"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/1b/54/0fcdab30ac31a594d699d909aa94c841fd94e173774f36e8c19e18536991/pyobjc_framework_coreaudio-10.3.2.tar.gz", hash = "sha256:c53e3247144b4f316cd588dd684a5f4edc6eebf9ca6beba488711b890bf0ff5f", size = 125996, upload-time = "2024-11-30T15:31:41.217Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/74/4e/bb75f2ce5505856752617af3d8b514cc99307248adc478755b23e5742e30/pyobjc_framework_CoreAudio-10.3.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f128f74b2895463a41b36296f7b01dde392fe08a11ddd642f8739f305ba51387", size = 35286, upload-time = "2024-11-30T13:24:24.01Z" },
    { url = "https://files.pythonhosted.org/packages/77/00/ab55d73350937c0509d2e492425d73faf22b8c329fce6aa0ce66c1fa4d6c/pyobjc_framework_CoreAudio-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:85d57b67269a246685c730994689bd42f5fb8f482e0f1650fc2d5c52360475ed", size = 37098, upload-time = "2024-11-30T13:24:43.013Z" },
]

[[package]]
name = "pyobjc-framework-corelocation"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ce/f4/e2a190a76bca21a435b59d467f9d1b53e5f6225a2a773accba3d2bb46077/pyobjc-framework-CoreLocation-9.2.tar.gz", hash = "sha256:a6a59e6d1297f84c99dfa9f4ac3927af2fc851b6a1999617f5c159571f885c43", size = 88391, upload-time = "2023-06-07T09:17:43.72Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/64/b6/72efff2b768e1bc2a0509a6fe08f49d26214724afc8322e8597c63df301d/pyobjc_framework_CoreLocation-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:9c03064c621657b994c3d7439eaafd21b7a524189ef4dbd6fba5819ec2d6e3ae", size = 12706, upload-time = "2023-06-07T07:59:01.367Z" },
    { url = "https://files.pythonhosted.org/packages/d1/fc/3feb10ee96038e90e10ca00df4611666d2490eb1dd358127090cbba92549/pyobjc_framework_CoreLocation-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:bf362a34332a986705af7e243cd018dbe33aa38db426b17de4f0df4bf5725ee1", size = 9368, upload-time = "2023-06-07T07:59:06.409Z" },
    { url = "https://files.pythonhosted.org/packages/51/b6/4beaf34a9eef6358a16797486ce77c3549d4568ee9efa5b7cefdf3238858/pyobjc_framework_CoreLocation-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:31c9041f398bc1335410e3ebc9f0a64e68bc7973bb1e7fc3468e266a18cb1049", size = 12729, upload-time = "2023-06-07T07:59:12.275Z" },
]

[[package]]
name = "pyobjc-framework-corelocation"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/25/a6/14450410f233a8e8d3ed1e48702a0b405f402bd3efa77b8bd62c424ca0fc/pyobjc_framework_corelocation-10.3.2.tar.gz", hash = "sha256:3fc543ff9b5a347bece0668e9c4d73cc94bf47624a723fad0d568d360567583f", size = 89656, upload-time = "2024-11-30T15:33:30.051Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/80/2f/53b16973362f6667dd9993d7dc68cd38b5df1e02b00ddf76b315654a0f0e/pyobjc_framework_CoreLocation-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:ee7a7b8ca885caa32dfe88acce2df4d5f8c26c5334ed3899ed860b382af3809c", size = 13058, upload-time = "2024-11-30T13:27:10.986Z" },
    { url = "https://files.pythonhosted.org/packages/c5/bf/f3ae97ea404e85cb0b5c4dfe58d35df35b0e20ed7b19b2eef5390a27a617/pyobjc_framework_CoreLocation-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:787837f678048e593ac21f0308156c237f1fcea07c4ce6d3a3a983074a87f14b", size = 12855, upload-time = "2024-11-30T13:27:11.937Z" },
    { url = "https://files.pythonhosted.org/packages/17/b1/3b5a40c95861e3ac5357276e434b78e85585f78e79a420922a67ddf2a16a/pyobjc_framework_CoreLocation-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:79d7306946e62a930d280be7496fce645d59190135a527b4df21cf9ad74b77a1", size = 12827, upload-time = "2024-11-30T13:27:12.775Z" },
    { url = "https://files.pythonhosted.org/packages/75/bd/a2c6400680103b28f9ef454d159116b08344c2214b20ec2caf610090cdce/pyobjc_framework_CoreLocation-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:eae5f2e857672f4c771aeb96aee7103a45c12f987adae230f23ef4ff23b40914", size = 9767, upload-time = "2024-11-30T13:27:13.554Z" },
    { url = "https://files.pythonhosted.org/packages/e8/1b/ba7436abd8eba1b016e5a4385bdbcc44c0b9a2760f9424ce54e80af9833e/pyobjc_framework_CoreLocation-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:3882873ec834531e1bbd641b56c591d8c15b016a4a959e3782459b51e4eddf79", size = 12794, upload-time = "2024-11-30T13:27:45.561Z" },
]

[[package]]
name = "pyobjc-framework-coremedia"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a9/2f/4131ac7b4a400b3767c40fb7f17f46d0caf8dc66b6ae400d1489fc596329/pyobjc-framework-CoreMedia-9.2.tar.gz", hash = "sha256:6345b47775aea573082e9a81bd52a3e19ce1a7d74f5064046909a8007e68dbe9", size = 169875, upload-time = "2023-06-07T09:19:18.563Z" }

[[package]]
name = "pyobjc-framework-coremedia"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fc/99/01b557daec18114166ae5fb602437477a60325e08dd9cfa5aac9d1c5174c/pyobjc_framework_coremedia-10.3.2.tar.gz", hash = "sha256:cf69753c12cd193df5ff25eb8f6da857c9aa93e73b8e497ddd77a3f48d1b171c", size = 181120, upload-time = "2024-11-30T15:33:52.574Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1d/fd/30c72d93812773719c6a72851aa10275dc637c7745ae90c2c64bde9d4eea/pyobjc_framework_CoreMedia-10.3.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:74d37893cd673de232fee25f9c782841971791164c1e9106893135269e7d776e", size = 28552, upload-time = "2024-11-30T13:31:45.619Z" },
    { url = "https://files.pythonhosted.org/packages/e7/47/74c2bfec3c83bb71d8c30d9996736568225010f38e7037bf82fc454576df/pyobjc_framework_CoreMedia-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a92ed6e87f6d668e9a203e3abbbedde98c341af18f440fa6b0a8439c674d89d8", size = 28652, upload-time = "2024-11-30T13:32:02.446Z" },
]

[[package]]
name = "pyobjc-framework-coreml"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/3b/d3/e60f81465d3194eb6f311d415754ca90cd09ff7bcc5ebbacccc7b1cc6bf3/pyobjc-framework-CoreML-9.2.tar.gz", hash = "sha256:b6d66824ab248a648a98e6f6d3151756e98c9d85ebe5d2a8a3650af5a5e88c5c", size = 71319, upload-time = "2023-06-07T09:18:28.589Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/cb/eb/b37c3a7351520f45eee1f8c78ca4bc916a247ec1ef7772f24a9b97870820/pyobjc_framework_CoreML-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:47e9d40ab8eb33245233d87c74eda747e43d785825fec0bbe8ad425f5362992e", size = 10168, upload-time = "2023-06-07T07:59:35.79Z" },
    { url = "https://files.pythonhosted.org/packages/d1/f7/0c077e73be8a8355ccf1150dfdfd67c8378f8980c282a5b5c0f2991c0078/pyobjc_framework_CoreML-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:f5cac466ce9280ee8620402c1b3462a7b526e0e5a5e3b9fdb936577a69cbab53", size = 8334, upload-time = "2023-06-07T07:59:39.899Z" },
    { url = "https://files.pythonhosted.org/packages/20/a2/533b06f309dc2b68e275dc0705c357ad6a370cdb2947bb46e4e7563764a1/pyobjc_framework_CoreML-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:4a8bfbdb8e88002d0ebdfa76de20cd411c307ba2f9fc0539ff7ad8d945ccd5cb", size = 10206, upload-time = "2023-06-07T07:59:44.456Z" },
]

[[package]]
name = "pyobjc-framework-coreml"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/be/7c/476d4459ce4d44d622365f721620f56fff7cebf50ade3560ae452244adaf/pyobjc_framework_coreml-10.3.2.tar.gz", hash = "sha256:f2e6eabe41fa34e964b707ba7a1269d5e049d5a7ac5574f35c4faa0647f385ba", size = 67101, upload-time = "2024-11-30T15:34:51.81Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/44/61/1693c4c7684be8eee011557eea95a16dcfe2045aad7a2ce5d6406185793a/pyobjc_framework_CoreML-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:fcac461545f007d648d2ff67f2734420c77173b467549e4a9d6b38a75dad2df7", size = 11793, upload-time = "2024-11-30T13:29:03.374Z" },
    { url = "https://files.pythonhosted.org/packages/84/17/ca68b24e0263d974a169f83cd597cc130e92741c0fbdca3c93e123ea2080/pyobjc_framework_CoreML-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:feea183b192cc806485b7713f135e544e7fa7ece3cea0e8cde92db4ae19374ab", size = 11553, upload-time = "2024-11-30T13:29:06.288Z" },
    { url = "https://files.pythonhosted.org/packages/66/4e/a939d232626b475f33727063bbcd5fda1f11a25e45c58ca52ff0005b8ece/pyobjc_framework_CoreML-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:15c89f9f37e46924357eb1c9859dfe4802a409263bb502b6a997046548097983", size = 11514, upload-time = "2024-11-30T13:29:43.283Z" },
    { url = "https://files.pythonhosted.org/packages/02/9d/4937bce9b3dff47a1bd822dbd2582aad6bf27ee6b7759d4120fa908327dc/pyobjc_framework_CoreML-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:a975f2667d7e5ad81091db5a89a27c0e091f20ac4be8de309b3b20d177d83637", size = 9006, upload-time = "2024-11-30T13:30:00.852Z" },
    { url = "https://files.pythonhosted.org/packages/8b/38/37ab623af9825bc5fb106feea54f46ebb06ca9c4f0c9bc73bdac949ac88c/pyobjc_framework_CoreML-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:559967fa7dd82e75cf84ae53b176ea6da8d7705e589213aea9fe10ac0ce1d100", size = 11491, upload-time = "2024-11-30T13:30:02.688Z" },
]

[[package]]
name = "pyobjc-framework-coreservices"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-fsevents", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/c0/6d/7f42e55281194169d4d41e97fd1aa2a761fe408b1f740576bcbf3c29faab/pyobjc-framework-CoreServices-9.2.tar.gz", hash = "sha256:cc0287dccc7089ad13b0a44472d7fc1ce804f06ea1f4c44c5a7c5c9eb8388d72", size = 865161, upload-time = "2023-06-07T09:24:10.603Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/06/9b/8c85a9b21ecfd266af2c07b683d4df320bb7f9e964f5511a58bb7b1e9048/pyobjc_framework_CoreServices-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:4997fbcc89bb6d025a2fb03bafab775caa9612f69e7f914d237884c39c0699ac", size = 29559, upload-time = "2023-06-07T08:01:39.916Z" },
    { url = "https://files.pythonhosted.org/packages/77/2f/652773ff0f3f514a95c23b23920d107ddf04df1bb4c4893b61b73681be26/pyobjc_framework_CoreServices-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:3c358d93ed094e9e545abc11f54c6ca70bf462a997546654062c7d3ed599e22c", size = 27787, upload-time = "2023-06-07T08:01:49.743Z" },
    { url = "https://files.pythonhosted.org/packages/3e/1b/e8f8609ed88def423f9f61cf65f05d326bfa04abc9e1bb44b61b115c5b77/pyobjc_framework_CoreServices-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:bfa3cc4a5cac5f51df4e598e552738abd605b0de35032c7aa1adcc895e84cfdc", size = 29543, upload-time = "2023-06-07T08:01:59.843Z" },
]

[[package]]
name = "pyobjc-framework-coreservices"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-fsevents", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0a/d2/2f5c63ad1b4f7c7c45c4da45cbeb3b13328d21794f5cec2b2018e42c177f/pyobjc_framework_coreservices-10.3.2.tar.gz", hash = "sha256:ee3cf8379839efe4300bbd33dca204ebe873e2671160fff856569976d45c68a8", size = 860352, upload-time = "2024-11-30T17:06:43.773Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/4e/8c/0111644312332e74efb96939581bb47570c5d24d6f41088388d0240c06d3/pyobjc_framework_CoreServices-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:e1e8f0490d27a07d3ea1ffd3e2c351c4a11ad3a208785d4f21b04afb6c6ad9ed", size = 29758, upload-time = "2024-11-30T13:37:26.799Z" },
    { url = "https://files.pythonhosted.org/packages/6e/e9/b36b9e111789b9bcf4ccc5ffa9fe87ba7a2e94a3da84d8cfc65753e4f379/pyobjc_framework_CoreServices-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:4512811b1c2737451b76969237ef5b8d7fd0e6b588652d50a1b6dc9fe3fa6226", size = 29714, upload-time = "2024-11-30T13:37:44.626Z" },
    { url = "https://files.pythonhosted.org/packages/85/87/6d96ee4520d27bc3776f7f8d4ab188a57b1031b3eb6269e1e8b7b1ef9938/pyobjc_framework_CoreServices-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:b73da63630903cb0d64138a933e92130ff3ad36770dd9da7b23047a3f362cc9f", size = 29708, upload-time = "2024-11-30T13:38:14.416Z" },
    { url = "https://files.pythonhosted.org/packages/16/74/9b40d27fb07ba6cf8ce389421d59bc5974bcbd5b47c2ec94e6071730ca40/pyobjc_framework_CoreServices-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:bbc1ac3fa0076c61221196346a715da32b0ff9c3f20cc5ebf59ba78688a40ad5", size = 28164, upload-time = "2024-11-30T13:38:40.324Z" },
    { url = "https://files.pythonhosted.org/packages/bd/a4/d28dff168700859df15e4dda7ac13f08185953e4c1d905bc20ba67b4b333/pyobjc_framework_CoreServices-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:40522a64a07276b8b577a71013f6c9272f35ebda3194d805d00f959c2ad26d83", size = 29762, upload-time = "2024-11-30T13:38:43.983Z" },
]

[[package]]
name = "pyobjc-framework-fsevents"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6e/95/77856ae09f6162e7ce670f2f8b7e2c7225365f16ca1ff5f29f60f9d4cf15/pyobjc-framework-FSEvents-9.2.tar.gz", hash = "sha256:910d3b3ae041a2a8f5bcb66749d705107ded384f9823ba44c54664a0c3ee9a65", size = 27444, upload-time = "2023-06-07T09:28:29.413Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2d/a9/ac02b984efd45d19267bc3c4bf39458fcfd4c1c16ead8239c0f459126f9c/pyobjc_framework_FSEvents-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:2328f8e125ba125489302a8800112cb65e0dade382e7fd6c2a8e21a66489b4e8", size = 13306, upload-time = "2023-06-07T08:05:12.804Z" },
    { url = "https://files.pythonhosted.org/packages/27/bb/5f738d25524958b4b4aeb2c8deb98d8cccd2845cd0bd37e46bf0eb019f09/pyobjc_framework_FSEvents-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:fca5d4a60313e71d0878c9e6010207c0762269f5f5941fb7cddff206921e9f2b", size = 8867, upload-time = "2023-06-07T08:05:16.754Z" },
    { url = "https://files.pythonhosted.org/packages/b1/22/f9ee83718a71e0138e60ecefcbfd77435c06c57c3c24311b45f15d946557/pyobjc_framework_FSEvents-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:7b3d4238d253bc609bd0ce50e385514d74ad706d07862eb66350e7d5faa02150", size = 13300, upload-time = "2023-06-07T08:05:21.913Z" },
]

[[package]]
name = "pyobjc-framework-fsevents"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/97/70/a37b1b8397bb3e23a8c15c78209f998d0294311a70b81104a5f22cbe9b26/pyobjc_framework_fsevents-10.3.2.tar.gz", hash = "sha256:fb215032d65aa39eb5af1b6481f605e71afa77f881b37ba804af77bf24d2fde3", size = 27720, upload-time = "2024-11-30T17:08:19.58Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c8/a9/5d37b56d89a2d4faf712e0f7dcfb1f6b938e0b5a263a6395261084fb2dac/pyobjc_framework_FSEvents-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:bca48481c75c6b95b792a3a5d06338b6a82d6e6f52fc83d30d0ba150f3695fd5", size = 13287, upload-time = "2024-11-30T13:51:46.094Z" },
    { url = "https://files.pythonhosted.org/packages/d0/99/628dc96c74256d5663aef13a133ab4ac8c01cf6fac306ad7721bf63e8d16/pyobjc_framework_FSEvents-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:a26f3f4f390584a55de16a2441fd7444de60ad677549c05a7c83c25498712564", size = 12944, upload-time = "2024-11-30T13:51:50.527Z" },
    { url = "https://files.pythonhosted.org/packages/25/63/f6cc9bcd34428084384f2ef8df96622128684a2f4015a5c73ecfda5a68c9/pyobjc_framework_FSEvents-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:a13389f7ac8dfe177c045c069dc224129f6f9b6871aa7892a4a1bc164fba99c1", size = 12938, upload-time = "2024-11-30T13:52:06.935Z" },
    { url = "https://files.pythonhosted.org/packages/9c/2c/1b705962aba38e701c3c8af1a870ebe09b796808203a396e630d0a696bf9/pyobjc_framework_FSEvents-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:aa2ea7bed475e69b3b1ec745e65bbaa4afd480cdef80600591f97a0bd1bece06", size = 8773, upload-time = "2024-11-30T13:52:37.283Z" },
    { url = "https://files.pythonhosted.org/packages/88/f0/a0ce3245a2e5505bacfbc079e45d9068485b7a9ac8a6fdd8f13ed633dce0/pyobjc_framework_FSEvents-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:5cbb808069ca184b7d75cc5cee2e18b1152d89b47f60a6be3aeaa918e03144f0", size = 12915, upload-time = "2024-11-30T13:52:39.944Z" },
]

[[package]]
name = "pyobjc-framework-metal"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/52/3c/5c797a8c6ea99815d87231a710a688ab87801aaceabffd7128882c9b22e3/pyobjc-framework-Metal-9.2.tar.gz", hash = "sha256:f483c331e07b587ef6477c79ae15a309297afa9a08c07ad9fafc4be82467d008", size = 349068, upload-time = "2023-06-07T09:39:46.667Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b6/c8/2f3165262e35a72cd1d397452e95140b9a6ce865efdeada6b92072547799/pyobjc_framework_Metal-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:fbec685b4240fdff0c05b8125dd999681beec604bb453abc7aa74acda68a9625", size = 55670, upload-time = "2023-06-07T08:12:01.957Z" },
    { url = "https://files.pythonhosted.org/packages/c9/a3/4405307a3f14ad2963f22a548b68b35e68397cf083f1ee891a3f042537f6/pyobjc_framework_Metal-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:4585ecda3fbd7460f047bf3b815315d4ccb0696163bb6a551e0b1b9d31bc5e85", size = 37687, upload-time = "2023-06-07T08:12:13.815Z" },
    { url = "https://files.pythonhosted.org/packages/e8/66/ae9da004a2bd055268a662524272298c168f4e1c5187a2f3e6110575e234/pyobjc_framework_Metal-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:4fab2a3cb2df366b8b1032b05ee4e2df5cc6d7d23b15774b85c4b3c60894bd32", size = 55382, upload-time = "2023-06-07T08:12:35.741Z" },
]

[[package]]
name = "pyobjc-framework-metal"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ef/12/a7695cab9ee18c2500ada306b283fc80f6628cb5fc396ee19fcc470bf186/pyobjc_framework_metal-10.3.2.tar.gz", hash = "sha256:59246982eab788b955f6d45dfb8c80e8f97bd1b56e1d678c90e91ad4a9376e35", size = 300113, upload-time = "2024-11-30T17:10:18.588Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/30/7c/921983793d8b3e7fc233bf9bc70f18ddde0f0d5ec9b80ef5e3203125b81b/pyobjc_framework_Metal-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b2b1b4027ff4c3aba7b05173503e88d4136f49b8378461d4d6e070be6cf504db", size = 55200, upload-time = "2024-11-30T14:22:05.641Z" },
    { url = "https://files.pythonhosted.org/packages/28/8c/b3eea5f2137694d107ffa276621d4e7b79fc2584f2144d27ee68eec85239/pyobjc_framework_Metal-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:3ba684bac796177c1646bf4da8d4acaa747f2598ca369eb8df8012db660e3cd5", size = 54712, upload-time = "2024-11-30T14:22:31.058Z" },
    { url = "https://files.pythonhosted.org/packages/c3/3f/d6013e14be2217dc86d2be68421fbab832e4630c2196265db4670d635316/pyobjc_framework_Metal-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:b83a72464df9e533e739fbc2a576a4d2c78bfedc826dcd4c821be9e08569bb44", size = 54843, upload-time = "2024-11-30T14:22:33.949Z" },
    { url = "https://files.pythonhosted.org/packages/a6/21/88549e155912110d8fff35856d4ecb034b5ad5c56ae52836f5db92beec86/pyobjc_framework_Metal-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:17b22be2a312ee6512c9118a5b18c4eeed264a796de39af81677e0e198c79066", size = 37366, upload-time = "2024-11-30T14:23:02.103Z" },
    { url = "https://files.pythonhosted.org/packages/5a/79/adbaf11e2cdb0b82a73f6d6d28a13bb553751314a503a16b6edc99968929/pyobjc_framework_Metal-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:34817e32470c4acdeb89b3fd8815c4e42ac27bcb034aa6d25b7855d97d48c15a", size = 54802, upload-time = "2024-11-30T14:23:05.033Z" },
]

[[package]]
name = "pyobjc-framework-photos"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/83/aa/de694bff02eb45e3ed8c661fa8902d099957a89697dab01ceafea2cbdec3/pyobjc-framework-Photos-9.2.tar.gz", hash = "sha256:b2a3c28eea5fced4dff85838fb59594e352bcc1f1fc997e7b1ab221b27c6056a", size = 103037, upload-time = "2023-06-07T09:46:52.179Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/55/3c/fa279f2401cf2fd77aa0185e5f4a08ea1712be22e4499fd7a8497064490f/pyobjc_framework_Photos-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:ae4f1358281196a12dbab79a9d3a7dfd60c2fe800771cfdd23ce0ee4e68c6211", size = 12683, upload-time = "2023-06-07T08:15:50.243Z" },
    { url = "https://files.pythonhosted.org/packages/08/fa/1ad66846c24971501f0ef6443fea9e84e6a1405264cae7e4a54941753817/pyobjc_framework_Photos-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:13591fe38225bcb5dfc15d408876f309ad5c5451e841ce899364ff4e687fe458", size = 9390, upload-time = "2023-06-07T08:15:55.735Z" },
    { url = "https://files.pythonhosted.org/packages/b1/21/499c81623c3f0bc43f861d7084371e7e16d4f24d5e5f3f24f93d54d2abf6/pyobjc_framework_Photos-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:6fbe2ceaf420db7ef3126ccef30d16e498acac343ca23a827bd8bcb5fb5bbb01", size = 12703, upload-time = "2023-06-07T08:15:57.624Z" },
]

[[package]]
name = "pyobjc-framework-photos"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ce/29/43357f5a2a57972bd4cdd4bbc5a914cee4e4eb1f9a9ba6b0aaed2f6308e3/pyobjc_framework_photos-10.3.2.tar.gz", hash = "sha256:4aa7180a45ef0b5245a277980c2be32195d6b512d66f8abbfdad480466e06434", size = 74548, upload-time = "2024-11-30T17:11:40.141Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/21/f0/98218afb34b42b4fbdbd62aefc3dcf8b706e16dea05e1b963888e150c28c/pyobjc_framework_Photos-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:7b1d3815a81efebfc24fb8cc70ba6fd49a9f261496b6a026a94d19e086b84fea", size = 12418, upload-time = "2024-11-30T14:38:12.807Z" },
    { url = "https://files.pythonhosted.org/packages/89/de/6335cefc3dedd876a2fa30bfb86ef3f83fc8dbd088c32d925b8735b65770/pyobjc_framework_Photos-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:2a8453c5069ae6929bbc0880a0979d4b72986541366e2d0c4665c0874cde832a", size = 12211, upload-time = "2024-11-30T14:38:15.234Z" },
    { url = "https://files.pythonhosted.org/packages/55/60/e5bc1fd38551bf8bfa90294fe196144c0b6e0a1202c0e5684be08bae339a/pyobjc_framework_Photos-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:95b88aaea9f96489195a9e9957d02588ed1968438998d2afcf0cb6b15d959670", size = 12170, upload-time = "2024-11-30T14:38:43.112Z" },
    { url = "https://files.pythonhosted.org/packages/26/32/a19d5e44d99b2a9b7e0e74ff3aca8256c7513c4258da873695454da8b658/pyobjc_framework_Photos-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:fa8edf4669c3ef6561f3cbafda9776f4183b358f492a77c67da1a8f515f72634", size = 9632, upload-time = "2024-11-30T14:39:02.589Z" },
    { url = "https://files.pythonhosted.org/packages/af/0d/dd7e6bc36b19610ed4a26db28814992d1c72136a246f06d82f8ae9bd5e07/pyobjc_framework_Photos-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:83bf410aa6e6dfdd0168df4ce2962cdb2a92c73e8422962642010467d0fd1749", size = 12574, upload-time = "2024-11-30T14:39:05.21Z" },
]

[[package]]
name = "pyobjc-framework-quartz"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/49/52/a56bbd76bba721f49fa07d34ac962414b95eb49a9b941fe4d3761f3e6934/pyobjc-framework-Quartz-9.2.tar.gz", hash = "sha256:f586183b9b9ef7f165f0444a7b714ed965d79f6e92617caaf869150dcfd5a72b", size = 3905351, upload-time = "2023-06-07T10:06:13.765Z" }

[[package]]
name = "pyobjc-framework-quartz"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/eb/bd/d78c845a6f0640975e837d1d0f04d6bbd95bb88b77dcee22482144ac5ad0/pyobjc_framework_quartz-10.3.2.tar.gz", hash = "sha256:193e7752c93e2d1304f914e3a8c069f4b66de237376c5285ba7c72e9ee0e3b15", size = 3776754, upload-time = "2024-11-30T17:11:47.99Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/91/31/514b9b7c20fb8347dce5cdaa0934253a9c2c637d3f05b8f6ab1bb7fbbb4f/pyobjc_framework_Quartz-10.3.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:9e27fb446e012c9571bc163cff5f3036e9e6fa5caca06b5d7882ad1c6b6aaf0c", size = 209167, upload-time = "2024-11-30T14:43:52.842Z" },
    { url = "https://files.pythonhosted.org/packages/ed/8f/6c23066cfc3c65c9769ac0fb9696c94ce36dc81dba48270f9b4810ee72d6/pyobjc_framework_Quartz-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:d5bd6ef96a3d08c97cf2aca43a819113cdff494b5abebcedd7cf23b6d6e711f4", size = 213534, upload-time = "2024-11-30T14:44:24.462Z" },
]

[[package]]
name = "pyobjc-framework-scriptingbridge"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/bc/20/3fa0549df9ec90015d2f666438f51454aa309e935707ac6f7d90ccac3eaa/pyobjc-framework-ScriptingBridge-9.2.tar.gz", hash = "sha256:48adc2a2b27f8f699f8d9e849c04b0a05afae8044d0435bc0765cdb79f42c051", size = 21354, upload-time = "2023-06-07T10:07:46.995Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6a/a6/bc1415797569dae5cd8380b67fdbd4a67efe0fd8aaa623256cc4670db95a/pyobjc_framework_ScriptingBridge-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:e7b2699b48692bd413611c664a7d218389270d46d6556be9aea29f3053c1baeb", size = 8888, upload-time = "2023-06-07T08:22:22.719Z" },
    { url = "https://files.pythonhosted.org/packages/77/22/3450c4a7739d1574731db2ce5f84c6bb1a3b0c15353c0af563b4bff25c7d/pyobjc_framework_ScriptingBridge-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:d22d0c3f0fb402a15fc820af6e26ce9c8bb539f9ec9acf78e10e71c2bac87703", size = 6592, upload-time = "2023-06-07T08:22:26.085Z" },
    { url = "https://files.pythonhosted.org/packages/ed/93/955c2b17abaf4f61dd3963d3cdca7708a5bdb31ea08fdad8282b4857e809/pyobjc_framework_ScriptingBridge-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:d80ed78995196d3950b520f9aac3ff2165b187f222860ef712a7eaa6e2525fd3", size = 8887, upload-time = "2023-06-07T08:22:30.848Z" },
]

[[package]]
name = "pyobjc-framework-scriptingbridge"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ed/0c/fff6cf7279cb78e8bd09a9a605d06f6e53a7d7d6b8a5c80f66477010d68b/pyobjc_framework_scriptingbridge-10.3.2.tar.gz", hash = "sha256:07655aff60a238fcf25918bd62fda007aef6076a92c47ea543dd71028e812a8c", size = 21176, upload-time = "2024-11-30T17:12:45.507Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9a/09/1d41d5d4ac20397ec99a162b6638cdc93860810c92e739b1d57f0f0bf72a/pyobjc_framework_ScriptingBridge-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:838a1a9f1d03f110780c273c356ebe255949f6bdb6487c8bd26fa8983fdf08b4", size = 8521, upload-time = "2024-11-30T14:53:51.249Z" },
    { url = "https://files.pythonhosted.org/packages/d3/a2/12a2444f9ee7554e6a8b1b038dea9dbc2c3e4c3f9f50ec6c1b9726e4c3b2/pyobjc_framework_ScriptingBridge-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:f045ba439b8ba13febb76254c5a21ba9f76c82a0e27f0f414b5f782625f2e46f", size = 8318, upload-time = "2024-11-30T14:53:55.072Z" },
    { url = "https://files.pythonhosted.org/packages/9b/1c/6654a91890627904f68c75d796d13e241f71a5b868f68adc36ec92662f6b/pyobjc_framework_ScriptingBridge-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:9223cd568170f6842df6bdf2d6719a3719b977e91a8d8e531d1a1eb0ef45c302", size = 8299, upload-time = "2024-11-30T14:54:38.852Z" },
    { url = "https://files.pythonhosted.org/packages/47/23/222e3b61927366ba94c3ba591b96b13f07f4b4cc52fc0b3588c822332164/pyobjc_framework_ScriptingBridge-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:dc4db637b1422c47b8aa4d33319f216de116832ef16fe1195e84e6fb7ca8f732", size = 6451, upload-time = "2024-11-30T14:54:59.4Z" },
    { url = "https://files.pythonhosted.org/packages/ed/aa/96bb253340c58403904089ff0235da77970ec816337706701456241f95ac/pyobjc_framework_ScriptingBridge-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:0d99ba4d7ed9a538b666f3aa81bd94b298f6663361dc3bccfe2718d9e28f1a2c", size = 8480, upload-time = "2024-11-30T14:55:02.272Z" },
]

[[package]]
name = "pyobjc-framework-uniformtypeidentifiers"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/51/83/c5af99327381c85787d54f8d71573741e658d12e114d6f97cf98af9824b5/pyobjc-framework-UniformTypeIdentifiers-9.2.tar.gz", hash = "sha256:dd5a4c64018f28abdc0232b2d50cb3cd9a46b9175effffb7ac958abc27f412a8", size = 17710, upload-time = "2023-06-07T10:12:33.24Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/72/a1/05bb4ccb0f6d20057c01d3f31b5b7a5dc70f8515f00597e74e11f0996654/pyobjc_framework_UniformTypeIdentifiers-9.2-py2.py3-none-any.whl", hash = "sha256:390a175f8ee279cef540151d4f55ec20a71faca2b5a0070eddd5cc9d943d3f02", size = 4250, upload-time = "2023-06-07T08:26:54.587Z" },
]

[[package]]
name = "pyobjc-framework-vision"
version = "9.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release < '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreml", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/07/9b/f5a99e28dd81a717cc7735edd3c1ad45b7e196c449ba84201ab31b82235c/pyobjc-framework-Vision-9.2.tar.gz", hash = "sha256:648d2224e85d45068aa8aa80527f7ae8d9d0af3bcb2a2a6e555c58e2313b3753", size = 100841, upload-time = "2023-06-07T10:14:10.075Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/69/d7/31c1093bb8b2d199208bda5c83240bf2a5d8cbb73f70dd60158be9fb3a48/pyobjc_framework_Vision-9.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:98e70f38df47d9f97f24ec7f5b0b060d8cf3d2937207d51edf3ddc9df7bbf18d", size = 21018, upload-time = "2023-06-07T08:27:39.008Z" },
    { url = "https://files.pythonhosted.org/packages/bf/28/e4820cc002f64dd36ed46e6dbb84e9d29d5e5de2b46c0afa983ea2697baa/pyobjc_framework_Vision-9.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:dd92fed0b3b931f33ab4c12f29ffccce59df50676d418c8952d4276a1f2ffafe", size = 13973, upload-time = "2023-06-07T08:27:40.903Z" },
    { url = "https://files.pythonhosted.org/packages/4a/f7/fbaf2b43719bd1d6b9a16d61b40e46f6a8bf1d1c34acad468ed461af81ac/pyobjc_framework_Vision-9.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:78668ce362925150bd74efa29881af5a61fb0ad431da9d4509c422fe17aadd62", size = 16198, upload-time = "2023-06-07T08:27:48.501Z" },
]

[[package]]
name = "pyobjc-framework-vision"
version = "10.3.2"
source = { registry = "https://pypi.org/simple" }
resolution-markers = [
    "platform_release >= '22.0' and sys_platform == 'darwin'",
]
dependencies = [
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-cocoa", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreml", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-quartz", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a9/f9/f9063b8cdbb2210b51beadffabb7021d55a20b3e9693219c53e98d067c10/pyobjc_framework_vision-10.3.2.tar.gz", hash = "sha256:5cfea4a750657e2c8e7c8b0c26c7aac2578ba09ab8f66ffa0e2ee632410cacf3", size = 108990, upload-time = "2024-11-30T17:14:14.602Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/1d/17/8712cf8e722ec3699b6cffd5a949a1f0269ede916659df84753090e08deb/pyobjc_framework_Vision-10.3.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:62efeeef9317d6014b26e4476de45f4b2853970272e1a236e45044ad8ac8b2fb", size = 17689, upload-time = "2024-11-30T15:19:31.787Z" },
    { url = "https://files.pythonhosted.org/packages/af/ef/16c0b66793d538402b125db5d579e18a40ac7163f154a2190a93a88796af/pyobjc_framework_Vision-10.3.2-cp36-abi3-macosx_10_13_universal2.whl", hash = "sha256:cae03536f12ed5764ecfdcf9cf96b37e577cc6e8c466aeb23a6aa0682b45ae39", size = 17546, upload-time = "2024-11-30T15:19:51.034Z" },
    { url = "https://files.pythonhosted.org/packages/ec/2b/16ed6ddea51eca88c7b9676431d7b35767b9b97c10e25ec8b5d762009923/pyobjc_framework_Vision-10.3.2-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:ba5ccd0bf12c29c2cdf1b52405c395929b5802e9120476b8e9a01af691ab33dc", size = 22021, upload-time = "2024-11-30T15:19:54.69Z" },
    { url = "https://files.pythonhosted.org/packages/ee/b5/02bd6bd54c456962ea9b1a09be96ce7af936e40b57555f035a3d79204d47/pyobjc_framework_Vision-10.3.2-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:2b7edc178ebeb621ba9a239449f8ae1fc6b643f60914ff2be4dad69e901ca331", size = 15580, upload-time = "2024-11-30T15:19:56.808Z" },
    { url = "https://files.pythonhosted.org/packages/b1/24/13648f9449a2406c0134f35cbdebe124c571b275b7b3061cf7bf3ceaf8ab/pyobjc_framework_Vision-10.3.2-cp36-abi3-macosx_11_0_universal2.whl", hash = "sha256:1083e23ee4dae7cca8e2d094b1995909690b277c967975227d3395222c0c7377", size = 17469, upload-time = "2024-11-30T15:19:57.755Z" },
]

[[package]]
name = "pytest"
version = "8.4.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/fb/aa/405082ce2749be5398045152251ac69c0f3578c7077efc53431303af97ce/pytest-8.4.0.tar.gz", hash = "sha256:14d920b48472ea0dbf68e45b96cd1ffda4705f33307dcc86c676c1b5104838a6", size = 1515232, upload-time = "2025-06-02T17:36:30.03Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/2f/de/afa024cbe022b1b318a3d224125aa24939e99b4ff6f22e0ba639a2eaee47/pytest-8.4.0-py3-none-any.whl", hash = "sha256:f40f825768ad76c0977cbacdf1fd37c6f7a468e460ea6a0636078f8972d4517e", size = 363797, upload-time = "2025-06-02T17:36:27.859Z" },
]

[[package]]
name = "pytimeparse2"
version = "1.7.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/19/10/cc63fecd69905eb4d300fe71bd580e4a631483e9f53fdcb8c0ad345ce832/pytimeparse2-1.7.1.tar.gz", hash = "sha256:98668cdcba4890e1789e432e8ea0059ccf72402f13f5d52be15bdfaeb3a8b253", size = 10431, upload-time = "2023-05-11T21:40:55.774Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/17/9e/85abf91ef5df452f56498927affdb7128194d15644084f6c6722477c305b/pytimeparse2-1.7.1-py3-none-any.whl", hash = "sha256:a162ea6a7707fd0bb82dd99556efb783935f51885c8bdced0fce3fffe85ab002", size = 6136, upload-time = "2023-05-11T21:40:46.051Z" },
]

[[package]]
name = "pyyaml"
version = "6.0.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/54/ed/79a089b6be93607fa5cdaedf301d7dfb23af5f25c398d5ead2525b063e17/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e", size = 130631, upload-time = "2024-08-06T20:33:50.674Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ef/e3/3af305b830494fa85d95f6d95ef7fa73f2ee1cc8ef5b495c7c3269fb835f/PyYAML-6.0.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:efdca5630322a10774e8e98e1af481aad470dd62c3170801852d752aa7a783ba", size = 181309, upload-time = "2024-08-06T20:32:43.4Z" },
    { url = "https://files.pythonhosted.org/packages/45/9f/3b1c20a0b7a3200524eb0076cc027a970d320bd3a6592873c85c92a08731/PyYAML-6.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:50187695423ffe49e2deacb8cd10510bc361faac997de9efef88badc3bb9e2d1", size = 171679, upload-time = "2024-08-06T20:32:44.801Z" },
    { url = "https://files.pythonhosted.org/packages/7c/9a/337322f27005c33bcb656c655fa78325b730324c78620e8328ae28b64d0c/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0ffe8360bab4910ef1b9e87fb812d8bc0a308b0d0eef8c8f44e0254ab3b07133", size = 733428, upload-time = "2024-08-06T20:32:46.432Z" },
    { url = "https://files.pythonhosted.org/packages/a3/69/864fbe19e6c18ea3cc196cbe5d392175b4cf3d5d0ac1403ec3f2d237ebb5/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:17e311b6c678207928d649faa7cb0d7b4c26a0ba73d41e99c4fff6b6c3276484", size = 763361, upload-time = "2024-08-06T20:32:51.188Z" },
    { url = "https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5", size = 759523, upload-time = "2024-08-06T20:32:53.019Z" },
    { url = "https://files.pythonhosted.org/packages/2b/b2/e3234f59ba06559c6ff63c4e10baea10e5e7df868092bf9ab40e5b9c56b6/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:41e4e3953a79407c794916fa277a82531dd93aad34e29c2a514c2c0c5fe971cc", size = 726660, upload-time = "2024-08-06T20:32:54.708Z" },
    { url = "https://files.pythonhosted.org/packages/fe/0f/25911a9f080464c59fab9027482f822b86bf0608957a5fcc6eaac85aa515/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:68ccc6023a3400877818152ad9a1033e3db8625d899c72eacb5a668902e4d652", size = 751597, upload-time = "2024-08-06T20:32:56.985Z" },
    { url = "https://files.pythonhosted.org/packages/14/0d/e2c3b43bbce3cf6bd97c840b46088a3031085179e596d4929729d8d68270/PyYAML-6.0.2-cp313-cp313-win32.whl", hash = "sha256:bc2fa7c6b47d6bc618dd7fb02ef6fdedb1090ec036abab80d4681424b84c1183", size = 140527, upload-time = "2024-08-06T20:33:03.001Z" },
    { url = "https://files.pythonhosted.org/packages/fa/de/02b54f42487e3d3c6efb3f89428677074ca7bf43aae402517bc7cca949f3/PyYAML-6.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:8388ee1976c416731879ac16da0aff3f63b286ffdd57cdeb95f3f2e085687563", size = 156446, upload-time = "2024-08-06T20:33:04.33Z" },
]

[[package]]
name = "requests"
version = "2.32.4"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://files.pythonhosted.org/packages/e1/0a/929373653770d8a0d7ea76c37de6e41f11eb07559b103b1c02cafb3f7cf8/requests-2.32.4.tar.gz", hash = "sha256:27d0316682c8a29834d3264820024b62a36942083d52caf2f14c0591336d3422", size = 135258, upload-time = "2025-06-09T16:43:07.34Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl", hash = "sha256:27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c", size = 64847, upload-time = "2025-06-09T16:43:05.728Z" },
]

[[package]]
name = "rich"
version = "13.9.4"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "markdown-it-py" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/ab/3a/0316b28d0761c6734d6bc14e770d85506c986c85ffb239e688eeaab2c2bc/rich-13.9.4.tar.gz", hash = "sha256:439594978a49a09530cff7ebc4b5c7103ef57baf48d5ea3184f21d9a2befa098", size = 223149, upload-time = "2024-11-01T16:43:57.873Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/19/71/39c7c0d87f8d4e6c020a393182060eaefeeae6c01dab6a84ec346f2567df/rich-13.9.4-py3-none-any.whl", hash = "sha256:6049d5e6ec054bf2779ab3358186963bac2ea89175919d699e378b99738c2a90", size = 242424, upload-time = "2024-11-01T16:43:55.817Z" },
]

[[package]]
name = "rich-theme-manager"
version = "0.11.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "rich" },
]
sdist = { url = "https://files.pythonhosted.org/packages/33/27/8139d912017d8d885b44143235959ea7db679ad4bde8dd4e6d99b3e35517/rich-theme-manager-0.11.0.tar.gz", hash = "sha256:3bc1effa4b6c42f72994b73c8b3c391b1c6e803deccc2fc3932da31b00f1a112", size = 17416, upload-time = "2022-05-01T15:09:30.997Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/68/ed/bd13b916738781c860fe97fa7b0db73281d93f7989dd4f566c9f99846a93/rich_theme_manager-0.11.0-py3-none-any.whl", hash = "sha256:b9155233076eb4f9fc888ef8cf7755a3cd8efa3bfa33cee5137fc00000117d8e", size = 14881, upload-time = "2022-05-01T15:09:32.273Z" },
]

[[package]]
name = "shortuuid"
version = "1.0.13"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/8c/e2/bcf761f3bff95856203f9559baf3741c416071dd200c0fc19fad7f078f86/shortuuid-1.0.13.tar.gz", hash = "sha256:3bb9cf07f606260584b1df46399c0b87dd84773e7b25912b7e391e30797c5e72", size = 9662, upload-time = "2024-03-11T20:11:06.879Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/c0/44/21d6bf170bf40b41396480d8d49ad640bca3f2b02139cd52aa1e272830a5/shortuuid-1.0.13-py3-none-any.whl", hash = "sha256:a482a497300b49b4953e15108a7913244e1bb0d41f9d332f5e9925dba33a3c5a", size = 10529, upload-time = "2024-03-11T20:11:04.807Z" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/94/e7/b2c673351809dca68a0e064b6af791aa332cf192da575fd474ed7d6f16a2/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81", size = 34031, upload-time = "2024-12-04T17:35:28.174Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274", size = 11050, upload-time = "2024-12-04T17:35:26.475Z" },
]

[[package]]
name = "strpdatetime"
version = "0.4.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "textx" },
]
sdist = { url = "https://files.pythonhosted.org/packages/f5/de/c4fcf047e38304784a930d2d6a299722c992e39db544308a55d89700a0c8/strpdatetime-0.4.0.tar.gz", hash = "sha256:bd05e902184f558484bfe9c162b50896d7c0dc935bdeb9a397c48df2029f008b", size = 26211, upload-time = "2024-11-25T13:24:10.405Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/50/52/7ac54b6823f426ea631ab7db35dfe97070ff77cb35179f35936bc204ba59/strpdatetime-0.4.0-py3-none-any.whl", hash = "sha256:69ac26633db1d299c77e858db3756669ad8d2954fec4a449088b2c8aed55b360", size = 17747, upload-time = "2024-11-25T13:24:09.455Z" },
]

[[package]]
name = "tenacity"
version = "8.5.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a3/4d/6a19536c50b849338fcbe9290d562b52cbdcf30d8963d3588a68a4107df1/tenacity-8.5.0.tar.gz", hash = "sha256:8bc6c0c8a09b31e6cad13c47afbed1a567518250a9a171418582ed8d9c20ca78", size = 47309, upload-time = "2024-07-05T07:25:31.836Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d2/3f/8ba87d9e287b9d385a02a7114ddcef61b26f86411e121c9003eb509a1773/tenacity-8.5.0-py3-none-any.whl", hash = "sha256:b594c2a5945830c267ce6b79a166228323ed52718f30302c1359836112346687", size = 28165, upload-time = "2024-07-05T07:25:29.591Z" },
]

[[package]]
name = "textx"
version = "4.2.2"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "arpeggio" },
]
sdist = { url = "https://files.pythonhosted.org/packages/0a/35/b762046953af54e97d3cd3a95af5a718c309d7d8387243e2f960eb6866d6/textx-4.2.2.tar.gz", hash = "sha256:62a84bfe6b956c3f3d221d34bd9c133268db0af16e35197a48932b17e9413ede", size = 2136969, upload-time = "2025-05-11T09:06:28.627Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/f9/85/fd56c2662d40b41a87176db954a24bfc7d1556123cf89b740d88980527d6/textx-4.2.2-py3-none-any.whl", hash = "sha256:4079315f0a0d1be5d36c35d1c693cab1246a08102f6578623db517cab8c04494", size = 67655, upload-time = "2025-05-11T09:06:25.28Z" },
]

[[package]]
name = "toml"
version = "0.10.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/be/ba/1f744cdc819428fc6b5084ec34d9b30660f6f9daaf70eead706e3203ec3c/toml-0.10.2.tar.gz", hash = "sha256:b3bda1d108d5dd99f4a20d24d9c348e91c4db7ab1b749200bded2f839ccbe68f", size = 22253, upload-time = "2020-11-01T01:40:22.204Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/44/6f/7120676b6d73228c96e17f1f794d8ab046fc910d781c8d151120c3f1569e/toml-0.10.2-py2.py3-none-any.whl", hash = "sha256:806143ae5bfb6a3c6e736a764057db0e6a0e05e338b5630894a5f779cabb4f9b", size = 16588, upload-time = "2020-11-01T01:40:20.672Z" },
]

[[package]]
name = "typing-extensions"
version = "4.14.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d1/bc/51647cd02527e87d05cb083ccc402f93e441606ff1f01739a62c8ad09ba5/typing_extensions-4.14.0.tar.gz", hash = "sha256:8676b788e32f02ab42d9e7c61324048ae4c6d844a399eebace3d4979d75ceef4", size = 107423, upload-time = "2025-06-02T14:52:11.399Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/69/e0/552843e0d356fbb5256d21449fa957fa4eff3bbc135a74a691ee70c7c5da/typing_extensions-4.14.0-py3-none-any.whl", hash = "sha256:a1514509136dd0b477638fc68d6a91497af5076466ad0fa6c338e44e359944af", size = 43839, upload-time = "2025-06-02T14:52:10.026Z" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/95/32/1a225d6164441be760d75c2c42e2780dc0873fe382da3e98a2e1e48361e5/tzdata-2025.2.tar.gz", hash = "sha256:b60a638fcc0daffadf82fe0f57e53d06bdec2f36c4df66280ae79bce6bd6f2b9", size = 196380, upload-time = "2025-03-23T13:54:43.652Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/5c/23/c7abc0ca0a1526a0774eca151daeb8de62ec457e77262b66b359c3c7679e/tzdata-2025.2-py2.py3-none-any.whl", hash = "sha256:1a403fada01ff9221ca8044d701868fa132215d84beb92242d9acd2147f667a8", size = 347839, upload-time = "2025-03-23T13:54:41.845Z" },
]

[[package]]
name = "urllib3"
version = "2.4.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/8a/78/16493d9c386d8e60e442a35feac5e00f0913c0f4b7c217c11e8ec2ff53e0/urllib3-2.4.0.tar.gz", hash = "sha256:414bc6535b787febd7567804cc015fee39daab8ad86268f1310a9250697de466", size = 390672, upload-time = "2025-04-10T15:23:39.232Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/6b/11/cc635220681e93a0183390e26485430ca2c7b5f9d33b15c74c2861cb8091/urllib3-2.4.0-py3-none-any.whl", hash = "sha256:4e16665048960a0900c702d4a66415956a584919c03361cac9f1df5c5dd7e813", size = 128680, upload-time = "2025-04-10T15:23:37.377Z" },
]

[[package]]
name = "utitools"
version = "0.3.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pyobjc-core", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-core", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreservices", version = "9.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release < '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-coreservices", version = "10.3.2", source = { registry = "https://pypi.org/simple" }, marker = "platform_release >= '22.0' and sys_platform == 'darwin'" },
    { name = "pyobjc-framework-uniformtypeidentifiers", marker = "platform_release >= '20.0' and sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/91/c3/0c46a24863365b32a38c8d6c87e336fb62acbde0540e349d4e650c33153e/utitools-0.3.0.tar.gz", hash = "sha256:a44bd564d9827c2e9fd55d463d241cdcf18c076c325f8d2d6131fa58934d388f", size = 28029, upload-time = "2024-12-15T14:44:09.776Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/72/d5/c477810758ea232302bd70a29665a22dd50473aabd2139ccf180c40f56b5/utitools-0.3.0-py3-none-any.whl", hash = "sha256:811be46b22e3a3db104044856a8a02c6d6ff13869e9679b825057160456eb5f9", size = 22962, upload-time = "2024-12-15T14:44:07.235Z" },
]

[[package]]
name = "wcwidth"
version = "0.2.13"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/6c/63/53559446a878410fc5a5974feb13d31d78d752eb18aeba59c7fef1af7598/wcwidth-0.2.13.tar.gz", hash = "sha256:72ea0c06399eb286d978fdedb6923a9eb47e1c486ce63e9b4e64fc18303972b5", size = 101301, upload-time = "2024-01-06T02:10:57.829Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fd/84/fd2ba7aafacbad3c4201d395674fc6348826569da3c0937e75505ead3528/wcwidth-0.2.13-py2.py3-none-any.whl", hash = "sha256:3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859", size = 34166, upload-time = "2024-01-06T02:10:55.763Z" },
]

[[package]]
name = "wheel"
version = "0.45.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/8a/98/2d9906746cdc6a6ef809ae6338005b3f21bb568bea3165cfc6a243fdc25c/wheel-0.45.1.tar.gz", hash = "sha256:661e1abd9198507b1409a20c02106d9670b2576e916d58f520316666abca6729", size = 107545, upload-time = "2024-11-23T00:18:23.513Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0b/2c/87f3254fd8ffd29e4c02732eee68a83a1d3c346ae39bc6822dcbcb697f2b/wheel-0.45.1-py3-none-any.whl", hash = "sha256:708e7481cc80179af0e556bbf0cc00b8444c7321e2700b8d8580231d13017248", size = 72494, upload-time = "2024-11-23T00:18:21.207Z" },
]

[[package]]
name = "whenever"
version = "0.8.5"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "tzdata", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/83/6e/d8081fe842dc829662bdb318bdad1256f4f3875cb0441294a6fa39eb9199/whenever-0.8.5.tar.gz", hash = "sha256:23c7e0119103ef71aab080caf332e17b2b8ee4cb5e0ab61b393263755c377e19", size = 234290, upload-time = "2025-06-09T07:39:04.502Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/23/4a/70bc93422f129aa9ea9397cc731e3ec9f0332d81642ae19ff2ec7b758abc/whenever-0.8.5-cp313-cp313-macosx_10_12_x86_64.whl", hash = "sha256:08c5c77c0387e3fda2726d312a68e374d441cd21ebe98802d07921ff6c6a7ecf", size = 401659, upload-time = "2025-06-09T07:38:49.831Z" },
    { url = "https://files.pythonhosted.org/packages/a3/51/b1fba840313edf65083c7afc68f796c9b5b5064db03dc5ccc6029600afaa/whenever-0.8.5-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:98c58420616b6c5ab824471e89c2fa0aff939ef28163e5bbfb7dfbea3d3f8098", size = 389978, upload-time = "2025-06-09T07:38:43.552Z" },
    { url = "https://files.pythonhosted.org/packages/ef/0b/c964f4842d8dc1f0372199ad9e9ed4276e8a419e2fbdf1f17e417e285cc9/whenever-0.8.5-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c4a3dbed6168f68f5bad63c7a543446c530eff102bb24504bc4d87bea29ef62c", size = 417973, upload-time = "2025-06-09T07:37:37.984Z" },
    { url = "https://files.pythonhosted.org/packages/3a/70/37b98f97a9364999e98ea9074775ca4c1df09fcded85e9bce3738373ad7d/whenever-0.8.5-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:ce9b1b4ef98ee92b80d1d2cf518f0767d631cbcd2df3ad4fd1d863fdad2c030c", size = 453330, upload-time = "2025-06-09T07:37:52.606Z" },
    { url = "https://files.pythonhosted.org/packages/5d/2a/a8c5cc159da33860e709720034bd39b39f94b8c191da2a0eeaf5d75659c9/whenever-0.8.5-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:739d608ee0586d0972d6f7789c705016e416b3ef8ae5337c1f73605feb23a784", size = 550280, upload-time = "2025-06-09T07:38:07.414Z" },
    { url = "https://files.pythonhosted.org/packages/1b/f8/edc24161c6cd3ee93363bd14b4e5ff679239f1d185a1f588d93b60b90221/whenever-0.8.5-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:4487c7684f7292e66b9a4c2bd6b5efbd7271c48c4410648d90e967dc5e0144ca", size = 463600, upload-time = "2025-06-09T07:38:13.611Z" },
    { url = "https://files.pythonhosted.org/packages/35/db/2f873f0854f577f16f20b651762acd4df279a3fa8b5f855113b609a15465/whenever-0.8.5-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ef58e82d825a763860bf1de61c28bba7ffa0a7da3a6456381d441caf6693f845", size = 433784, upload-time = "2025-06-09T07:38:32.05Z" },
    { url = "https://files.pythonhosted.org/packages/50/df/555db022bdab1fa39e7b5d3756da1841f499e569cb7fda74a356dba6b89b/whenever-0.8.5-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:f3992f8f3424ff52c5c21a95792b2dfbe537df08ab951ef7a0654ba517b7a28e", size = 481548, upload-time = "2025-06-09T07:38:19.627Z" },
    { url = "https://files.pythonhosted.org/packages/95/fe/d427a3e3d6ae9c69690f4c2a1b40e301d7abf45a2c6c21aa3040f25fe642/whenever-0.8.5-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:a1988f40fe0d28902aa3f132e4fb23a18599cf9ad49a54311265425631d8ec96", size = 595909, upload-time = "2025-06-09T07:37:44.649Z" },
    { url = "https://files.pythonhosted.org/packages/26/0c/c90389b0f52473cdfb30046ad9369b379ab924d9d2d3be590e09890d49a6/whenever-0.8.5-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:17d47930a318563180c06dcf3332db3956658751d2d29b367c8e550fdda34b2c", size = 716887, upload-time = "2025-06-09T07:38:00.784Z" },
    { url = "https://files.pythonhosted.org/packages/20/45/5e1f15b51a7311579d707fac7250e3c5312e7c2491972b85c1e5859b7fa0/whenever-0.8.5-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:0a28c299195fdf9082d7fa7d1aa985d709aef8c206160bd50b52d1913261af3d", size = 653268, upload-time = "2025-06-09T07:38:26.33Z" },
    { url = "https://files.pythonhosted.org/packages/c9/73/935c542a6ec07699773c231871f4fd1a727a2fba995599275bbe5ad0b500/whenever-0.8.5-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:4920995d03e26e225d2a733d9f58e3ed3cce7a33994b0bf2f6d94c9f85059dd4", size = 605528, upload-time = "2025-06-09T07:38:37.778Z" },
    { url = "https://files.pythonhosted.org/packages/82/15/bf5332b353239af8120db4934fa6e0b1a4f5772a56185070d85f9523c462/whenever-0.8.5-cp313-cp313-win32.whl", hash = "sha256:f67d1054de92486baf8d48a28c0e2ff5fc78ab2e772b054f69520953727862f5", size = 348552, upload-time = "2025-06-09T07:38:55.524Z" },
    { url = "https://files.pythonhosted.org/packages/a9/34/e539f26dff602085c001f15264ca508e3df3cd9a7fd50d0a51c3f9759976/whenever-0.8.5-cp313-cp313-win_amd64.whl", hash = "sha256:f7b7f1814fd3d216c8ff5d62076a46f21b38d03af71a59887efa3fc3e8d1c5bb", size = 343086, upload-time = "2025-06-09T07:39:01.864Z" },
]

[[package]]
name = "wrapt"
version = "1.17.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/c3/fc/e91cc220803d7bc4db93fb02facd8461c37364151b8494762cc88b0fbcef/wrapt-1.17.2.tar.gz", hash = "sha256:41388e9d4d1522446fe79d3213196bd9e3b301a336965b9e27ca2788ebd122f3", size = 55531, upload-time = "2025-01-14T10:35:45.465Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ce/b9/0ffd557a92f3b11d4c5d5e0c5e4ad057bd9eb8586615cdaf901409920b14/wrapt-1.17.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6ed6ffac43aecfe6d86ec5b74b06a5be33d5bb9243d055141e8cabb12aa08125", size = 53800, upload-time = "2025-01-14T10:34:21.571Z" },
    { url = "https://files.pythonhosted.org/packages/c0/ef/8be90a0b7e73c32e550c73cfb2fa09db62234227ece47b0e80a05073b375/wrapt-1.17.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:35621ae4c00e056adb0009f8e86e28eb4a41a4bfa8f9bfa9fca7d343fe94f998", size = 38824, upload-time = "2025-01-14T10:34:22.999Z" },
    { url = "https://files.pythonhosted.org/packages/36/89/0aae34c10fe524cce30fe5fc433210376bce94cf74d05b0d68344c8ba46e/wrapt-1.17.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:a604bf7a053f8362d27eb9fefd2097f82600b856d5abe996d623babd067b1ab5", size = 38920, upload-time = "2025-01-14T10:34:25.386Z" },
    { url = "https://files.pythonhosted.org/packages/3b/24/11c4510de906d77e0cfb5197f1b1445d4fec42c9a39ea853d482698ac681/wrapt-1.17.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5cbabee4f083b6b4cd282f5b817a867cf0b1028c54d445b7ec7cfe6505057cf8", size = 88690, upload-time = "2025-01-14T10:34:28.058Z" },
    { url = "https://files.pythonhosted.org/packages/71/d7/cfcf842291267bf455b3e266c0c29dcb675b5540ee8b50ba1699abf3af45/wrapt-1.17.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:49703ce2ddc220df165bd2962f8e03b84c89fee2d65e1c24a7defff6f988f4d6", size = 80861, upload-time = "2025-01-14T10:34:29.167Z" },
    { url = "https://files.pythonhosted.org/packages/d5/66/5d973e9f3e7370fd686fb47a9af3319418ed925c27d72ce16b791231576d/wrapt-1.17.2-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8112e52c5822fc4253f3901b676c55ddf288614dc7011634e2719718eaa187dc", size = 89174, upload-time = "2025-01-14T10:34:31.702Z" },
    { url = "https://files.pythonhosted.org/packages/a7/d3/8e17bb70f6ae25dabc1aaf990f86824e4fd98ee9cadf197054e068500d27/wrapt-1.17.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:9fee687dce376205d9a494e9c121e27183b2a3df18037f89d69bd7b35bcf59e2", size = 86721, upload-time = "2025-01-14T10:34:32.91Z" },
    { url = "https://files.pythonhosted.org/packages/6f/54/f170dfb278fe1c30d0ff864513cff526d624ab8de3254b20abb9cffedc24/wrapt-1.17.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:18983c537e04d11cf027fbb60a1e8dfd5190e2b60cc27bc0808e653e7b218d1b", size = 79763, upload-time = "2025-01-14T10:34:34.903Z" },
    { url = "https://files.pythonhosted.org/packages/4a/98/de07243751f1c4a9b15c76019250210dd3486ce098c3d80d5f729cba029c/wrapt-1.17.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:703919b1633412ab54bcf920ab388735832fdcb9f9a00ae49387f0fe67dad504", size = 87585, upload-time = "2025-01-14T10:34:36.13Z" },
    { url = "https://files.pythonhosted.org/packages/f9/f0/13925f4bd6548013038cdeb11ee2cbd4e37c30f8bfd5db9e5a2a370d6e20/wrapt-1.17.2-cp313-cp313-win32.whl", hash = "sha256:abbb9e76177c35d4e8568e58650aa6926040d6a9f6f03435b7a522bf1c487f9a", size = 36676, upload-time = "2025-01-14T10:34:37.962Z" },
    { url = "https://files.pythonhosted.org/packages/bf/ae/743f16ef8c2e3628df3ddfd652b7d4c555d12c84b53f3d8218498f4ade9b/wrapt-1.17.2-cp313-cp313-win_amd64.whl", hash = "sha256:69606d7bb691b50a4240ce6b22ebb319c1cfb164e5f6569835058196e0f3a845", size = 38871, upload-time = "2025-01-14T10:34:39.13Z" },
    { url = "https://files.pythonhosted.org/packages/3d/bc/30f903f891a82d402ffb5fda27ec1d621cc97cb74c16fea0b6141f1d4e87/wrapt-1.17.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4a721d3c943dae44f8e243b380cb645a709ba5bd35d3ad27bc2ed947e9c68192", size = 56312, upload-time = "2025-01-14T10:34:40.604Z" },
    { url = "https://files.pythonhosted.org/packages/8a/04/c97273eb491b5f1c918857cd26f314b74fc9b29224521f5b83f872253725/wrapt-1.17.2-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:766d8bbefcb9e00c3ac3b000d9acc51f1b399513f44d77dfe0eb026ad7c9a19b", size = 40062, upload-time = "2025-01-14T10:34:45.011Z" },
    { url = "https://files.pythonhosted.org/packages/4e/ca/3b7afa1eae3a9e7fefe499db9b96813f41828b9fdb016ee836c4c379dadb/wrapt-1.17.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:e496a8ce2c256da1eb98bd15803a79bee00fc351f5dfb9ea82594a3f058309e0", size = 40155, upload-time = "2025-01-14T10:34:47.25Z" },
    { url = "https://files.pythonhosted.org/packages/89/be/7c1baed43290775cb9030c774bc53c860db140397047cc49aedaf0a15477/wrapt-1.17.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:40d615e4fe22f4ad3528448c193b218e077656ca9ccb22ce2cb20db730f8d306", size = 113471, upload-time = "2025-01-14T10:34:50.934Z" },
    { url = "https://files.pythonhosted.org/packages/32/98/4ed894cf012b6d6aae5f5cc974006bdeb92f0241775addad3f8cd6ab71c8/wrapt-1.17.2-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a5aaeff38654462bc4b09023918b7f21790efb807f54c000a39d41d69cf552cb", size = 101208, upload-time = "2025-01-14T10:34:52.297Z" },
    { url = "https://files.pythonhosted.org/packages/ea/fd/0c30f2301ca94e655e5e057012e83284ce8c545df7661a78d8bfca2fac7a/wrapt-1.17.2-cp313-cp313t-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9a7d15bbd2bc99e92e39f49a04653062ee6085c0e18b3b7512a4f2fe91f2d681", size = 109339, upload-time = "2025-01-14T10:34:53.489Z" },
    { url = "https://files.pythonhosted.org/packages/75/56/05d000de894c4cfcb84bcd6b1df6214297b8089a7bd324c21a4765e49b14/wrapt-1.17.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:e3890b508a23299083e065f435a492b5435eba6e304a7114d2f919d400888cc6", size = 110232, upload-time = "2025-01-14T10:34:55.327Z" },
    { url = "https://files.pythonhosted.org/packages/53/f8/c3f6b2cf9b9277fb0813418e1503e68414cd036b3b099c823379c9575e6d/wrapt-1.17.2-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:8c8b293cd65ad716d13d8dd3624e42e5a19cc2a2f1acc74b30c2c13f15cb61a6", size = 100476, upload-time = "2025-01-14T10:34:58.055Z" },
    { url = "https://files.pythonhosted.org/packages/a7/b1/0bb11e29aa5139d90b770ebbfa167267b1fc548d2302c30c8f7572851738/wrapt-1.17.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:4c82b8785d98cdd9fed4cac84d765d234ed3251bd6afe34cb7ac523cb93e8b4f", size = 106377, upload-time = "2025-01-14T10:34:59.3Z" },
    { url = "https://files.pythonhosted.org/packages/6a/e1/0122853035b40b3f333bbb25f1939fc1045e21dd518f7f0922b60c156f7c/wrapt-1.17.2-cp313-cp313t-win32.whl", hash = "sha256:13e6afb7fe71fe7485a4550a8844cc9ffbe263c0f1a1eea569bc7091d4898555", size = 37986, upload-time = "2025-01-14T10:35:00.498Z" },
    { url = "https://files.pythonhosted.org/packages/09/5e/1655cf481e079c1f22d0cabdd4e51733679932718dc23bf2db175f329b76/wrapt-1.17.2-cp313-cp313t-win_amd64.whl", hash = "sha256:eaf675418ed6b3b31c7a989fd007fa7c3be66ce14e5c3b27336383604c9da85c", size = 40750, upload-time = "2025-01-14T10:35:03.378Z" },
    { url = "https://files.pythonhosted.org/packages/2d/82/f56956041adef78f849db6b289b282e72b55ab8045a75abad81898c28d19/wrapt-1.17.2-py3-none-any.whl", hash = "sha256:b18f2d1533a71f069c7f82d524a52599053d4c7166e9dd374ae2136b7f40f7c8", size = 23594, upload-time = "2025-01-14T10:35:44.018Z" },
]

[[package]]
name = "wurlitzer"
version = "3.1.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/33/90/623f99c55c7d0727a58eb2b7dfb65cb406c561a5c2e9a95b0d6a450c473d/wurlitzer-3.1.1.tar.gz", hash = "sha256:bfb9144ab9f02487d802b9ff89dbd3fa382d08f73e12db8adc4c2fb00cd39bd9", size = 11867, upload-time = "2024-06-12T10:27:30.089Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/9a/24/93ce54550a9dd3fd996ed477f00221f215bf6da3580397fbc138d6036e2e/wurlitzer-3.1.1-py3-none-any.whl", hash = "sha256:0b2749c2cde3ef640bf314a9f94b24d929fe1ca476974719a6909dfc568c3aac", size = 8590, upload-time = "2024-06-12T10:27:28.787Z" },
]

[[package]]
name = "xattr"
version = "1.1.4"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "cffi", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "https://files.pythonhosted.org/packages/62/bf/8b98081f9f8fd56d67b9478ff1e0f8c337cde08bcb92f0d592f0a7958983/xattr-1.1.4.tar.gz", hash = "sha256:b7b02ecb2270da5b7e7deaeea8f8b528c17368401c2b9d5f63e91f545b45d372", size = 16729, upload-time = "2025-01-06T19:19:32.557Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/41/7c/3b8e82ba6f5d24753314ef9922390d9c8e78f157159621bb01f4741d3240/xattr-1.1.4-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:878df1b38cfdadf3184ad8c7b0f516311128d5597b60ac0b3486948953658a83", size = 23910, upload-time = "2025-01-06T19:18:14.745Z" },
    { url = "https://files.pythonhosted.org/packages/77/8d/30b04121b42537aa969a797b89138bb1abd213d5777e9d4289284ebc7dee/xattr-1.1.4-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:0c9b8350244a1c5454f93a8d572628ff71d7e2fc2f7480dcf4c4f0e8af3150fe", size = 18890, upload-time = "2025-01-06T19:18:17.68Z" },
    { url = "https://files.pythonhosted.org/packages/fe/94/a95c7db010265a449935452db54d614afb1e5e91b1530c61485fc0fea4b5/xattr-1.1.4-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:a46bf48fb662b8bd745b78bef1074a1e08f41a531168de62b5d7bd331dadb11a", size = 19211, upload-time = "2025-01-06T19:18:24.625Z" },
]

[[package]]
name = "xdg-base-dirs"
version = "6.0.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/bf/d0/bbe05a15347538aaf9fa5b51ac3b97075dfb834931fcb77d81fbdb69e8f6/xdg_base_dirs-6.0.2.tar.gz", hash = "sha256:950504e14d27cf3c9cb37744680a43bf0ac42efefc4ef4acf98dc736cab2bced", size = 4085, upload-time = "2024-10-19T14:35:08.114Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/fc/03/030b47fd46b60fc87af548e57ff59c2ca84b2a1dadbe721bb0ce33896b2e/xdg_base_dirs-6.0.2-py3-none-any.whl", hash = "sha256:3c01d1b758ed4ace150ac960ac0bd13ce4542b9e2cdf01312dcda5012cfebabe", size = 4747, upload-time = "2024-10-19T14:35:05.931Z" },
]
